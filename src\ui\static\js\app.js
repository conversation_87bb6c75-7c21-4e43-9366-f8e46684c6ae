// SuperBot Vue应用 - 与Ant Design Vue集成的现代化界面
// 这个文件现在主要用于与pywebview后端API的通信和一些工具函数

class SuperBotAPI {
    constructor() {
        this.baseUrl = '';
        this.isConnected = false;
    }

    // 检查pywebview API是否可用
    isAvailable() {
        return typeof pywebview !== 'undefined' && pywebview.api;
    }

    // 系统状态相关API
    async getSystemStatus() {
        try {
            if (this.isAvailable()) {
                return await pywebview.api.get_system_status();
            } else {
                // 模拟数据用于开发测试
                return {
                    connected: true,
                    system_status: 'running',
                    ai_status: 'active',
                    trading_status: 'stopped',
                    api_status: 'connected'
                };
            }
        } catch (error) {
            console.error('获取系统状态失败:', error);
            throw error;
        }
    }

    // 账户信息相关API
    async getAccountInfo() {
        try {
            if (this.isAvailable()) {
                return await pywebview.api.get_account_info();
            } else {
                // 模拟数据
                return {
                    total_balance: 130839.39,
                    available_balance: 115628.49,
                    used_margin: 15210.90,
                    margin_ratio: 11.63,
                    balance_change: 2.34
                };
            }
        } catch (error) {
            console.error('获取账户信息失败:', error);
            throw error;
        }
    }

    /**
     * 绑定事件处理器
     */
    bindEvents() {
        // 标签页切换
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const tab = e.currentTarget.dataset.tab;
                this.switchTab(tab);
            });
        });

        // 交易控制按钮
        const startBtn = document.getElementById('start-trading-btn');
        const stopBtn = document.getElementById('stop-trading-btn');
        
        if (startBtn) {
            startBtn.addEventListener('click', () => this.startTrading());
        }
        
        if (stopBtn) {
            stopBtn.addEventListener('click', () => this.stopTrading());
        }

        // 配置保存按钮
        const saveConfigBtn = document.getElementById('save-config-btn');
        if (saveConfigBtn) {
            saveConfigBtn.addEventListener('click', () => this.saveConfig());
        }

        // 配置重置按钮
        const resetConfigBtn = document.getElementById('reset-config-btn');
        if (resetConfigBtn) {
            resetConfigBtn.addEventListener('click', () => this.resetConfig());
        }

        // 清空日志按钮
        const clearLogsBtn = document.getElementById('clear-logs-btn');
        if (clearLogsBtn) {
            clearLogsBtn.addEventListener('click', () => this.clearLogs());
        }

        // 窗口关闭事件
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }

    /**
     * 切换标签页
     * @param {string} tabName - 标签页名称
     */
    switchTab(tabName) {
        // 更新菜单状态
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        this.currentTab = tabName;

        // 根据标签页加载相应数据
        this.loadTabData(tabName);
    }

    /**
     * 加载标签页数据
     * @param {string} tabName - 标签页名称
     */
    async loadTabData(tabName) {
        switch (tabName) {
            case 'dashboard':
                await this.updateDashboard();
                break;
            case 'trading':
                await this.updateTradingPage();
                break;
            case 'positions':
                await this.updatePositionsPage();
                break;
            case 'config':
                await this.loadConfig();
                break;
            case 'logs':
                await this.updateLogsPage();
                break;
        }
    }

    /**
     * 检查API连接
     */
    async checkConnection() {
        try {
            const result = await api.ping();
            this.isConnected = result.success;
            this.updateConnectionStatus();
            
            if (this.isConnected) {
                console.log('API连接正常');
            } else {
                console.error('API连接失败');
            }
        } catch (error) {
            console.error('检查连接失败:', error);
            this.isConnected = false;
            this.updateConnectionStatus();
        }
    }

    /**
     * 更新连接状态显示
     */
    updateConnectionStatus() {
        const statusEl = document.getElementById('connection-status');
        if (statusEl) {
            const indicator = statusEl.querySelector('i');
            const text = statusEl.querySelector('span');
            
            if (this.isConnected) {
                statusEl.className = 'status-indicator connected';
                text.textContent = '已连接';
            } else {
                statusEl.className = 'status-indicator disconnected';
                text.textContent = '连接断开';
            }
        }
    }

    /**
     * 开始数据更新
     */
    startDataUpdate() {
        // 立即更新一次
        this.updateData();
        
        // 设置定时更新
        this.updateInterval = setInterval(() => {
            this.updateData();
        }, 5000); // 每5秒更新一次
    }

    /**
     * 停止数据更新
     */
    stopDataUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    /**
     * 更新数据
     */
    async updateData() {
        if (!this.isConnected) {
            await this.checkConnection();
            return;
        }

        try {
            // 并行获取数据
            const [systemStatus, tradingStatus] = await Promise.all([
                api.getSystemStatus(),
                api.getTradingStatus()
            ]);

            // 更新缓存
            if (systemStatus.success) {
                this.cache.systemStatus = systemStatus.data;
            }
            
            if (tradingStatus.success) {
                this.cache.tradingStatus = tradingStatus.data;
                this.tradingActive = tradingStatus.data.trading_active;
            }

            // 更新当前标签页
            this.loadTabData(this.currentTab);

        } catch (error) {
            console.error('更新数据失败:', error);
        }
    }

    /**
     * 更新仪表盘
     */
    async updateDashboard() {
        // 更新状态卡片
        this.updateStatusCards();

        // 更新账户余额
        await this.updateAccountBalance();

        // 更新市场数据
        await this.updateMarketData();

        // 更新仓位汇总
        await this.updatePositionSummary();
    }

    /**
     * 更新状态卡片
     */
    updateStatusCards() {
        const systemStatus = this.cache.systemStatus;
        const tradingStatus = this.cache.tradingStatus;

        // 系统状态
        const systemStatusEl = document.getElementById('system-status-text');
        if (systemStatusEl && systemStatus) {
            systemStatusEl.textContent = systemStatus.status === 'ready' ? '运行正常' : '初始化中';
        }

        // 交易状态
        const tradingStatusEl = document.getElementById('trading-status-text');
        const tradingIndicatorEl = document.getElementById('trading-indicator');
        if (tradingStatusEl && tradingStatus) {
            const isActive = tradingStatus.trading_active;
            tradingStatusEl.textContent = isActive ? '交易中' : '未启动';

            // 更新状态指示器
            if (tradingIndicatorEl) {
                if (isActive) {
                    tradingIndicatorEl.classList.add('active');
                } else {
                    tradingIndicatorEl.classList.remove('active');
                }
            }
        }

        // 任务调度状态
        const schedulerStatusEl = document.getElementById('scheduler-status-text');
        if (schedulerStatusEl && tradingStatus) {
            schedulerStatusEl.textContent = tradingStatus.scheduler_running ? '运行中' : '未运行';
        }

        // 持仓数量
        const positionsCountEl = document.getElementById('positions-count');
        if (positionsCountEl && this.cache.positions) {
            positionsCountEl.textContent = this.cache.positions.length.toString();
        }

        // AI引擎状态
        const aiStatusEl = document.getElementById('ai-status-text');
        if (aiStatusEl) {
            aiStatusEl.textContent = '就绪';
        }
    }

    /**
     * 更新最后更新时间
     */
    updateLastUpdateTime() {
        const lastUpdateEl = document.getElementById('last-update-time');
        if (lastUpdateEl) {
            const now = new Date();
            const timeStr = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            lastUpdateEl.textContent = timeStr + ' 更新';
        }
    }

    /**
     * 更新账户余额
     */
    async updateAccountBalance() {
        try {
            const result = await api.getAccountBalance();
            if (result.success) {
                this.cache.accountBalance = result.data;
                this.displayAccountBalance(result.data);
            }
        } catch (error) {
            console.error('获取账户余额失败:', error);
        }
    }

    /**
     * 显示账户余额
     * @param {Object} data - 账户余额数据
     */
    displayAccountBalance(data) {
        const totalBalanceEl = document.getElementById('total-balance');
        const availableBalanceEl = document.getElementById('available-balance');
        const usedMarginEl = document.getElementById('used-margin');
        const marginRatioEl = document.getElementById('margin-ratio');

        if (totalBalanceEl) {
            totalBalanceEl.textContent = Utils.formatCurrency(data.total_balance || 0);
        }

        if (availableBalanceEl) {
            availableBalanceEl.textContent = Utils.formatCurrency(data.available_balance || 0);
        }

        if (usedMarginEl) {
            usedMarginEl.textContent = Utils.formatCurrency(data.used_margin || 0);
        }

        // 计算保证金率
        if (marginRatioEl && data.total_balance > 0) {
            const marginRatio = ((data.used_margin || 0) / data.total_balance * 100).toFixed(2);
            marginRatioEl.textContent = marginRatio + '%';
        }

        // 更新最后更新时间
        this.updateLastUpdateTime();
    }

    /**
     * 更新市场数据
     */
    async updateMarketData() {
        try {
            const result = await api.getMarketData('BTC/USDT:USDT');
            if (result.success) {
                this.cache.marketData = result.data;
                this.displayMarketData(result.data);
            }
        } catch (error) {
            console.error('获取市场数据失败:', error);
        }
    }

    /**
     * 显示市场数据
     * @param {Object} data - 市场数据
     */
    displayMarketData(data) {
        const container = document.getElementById('market-data');
        if (!container) return;

        if (!data) {
            container.innerHTML = `
                <div class="market-loading">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>暂无市场数据</span>
                </div>
            `;
            return;
        }

        const changeClass = (data.change || 0) >= 0 ? 'positive' : 'negative';
        const changeIcon = (data.change || 0) >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';

        container.innerHTML = `
            <div class="market-item primary">
                <div class="market-label">${data.symbol || 'BTC/USDT'}</div>
                <div class="market-price">${Utils.formatCurrency(data.price || 0)}</div>
                <div class="market-change ${changeClass}">
                    <i class="fas ${changeIcon}"></i>
                    ${Utils.formatPercent(data.change || 0)}
                </div>
            </div>
            <div class="market-stats">
                <div class="stat-item">
                    <div class="stat-label">24h最高</div>
                    <div class="stat-value">${Utils.formatCurrency(data.high || 0)}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">24h最低</div>
                    <div class="stat-value">${Utils.formatCurrency(data.low || 0)}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">24h成交量</div>
                    <div class="stat-value">${Utils.formatNumber(data.volume || 0)}</div>
                </div>
            </div>
        `;
    }

    /**
     * 更新仓位汇总
     */
    async updatePositionSummary() {
        try {
            const result = await api.getPositionSummary();
            if (result.success) {
                this.cache.positionSummary = result.data;
                this.displayPositionSummary(result.data);
            }
        } catch (error) {
            console.error('获取仓位汇总失败:', error);
        }
    }

    /**
     * 显示仓位汇总
     * @param {Object} data - 仓位汇总数据
     */
    displayPositionSummary(data) {
        const container = document.getElementById('position-summary');
        if (!container) return;

        if (!data) {
            container.innerHTML = `
                <div class="position-loading">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>暂无仓位数据</span>
                </div>
            `;
            return;
        }

        const pnlClass = (data.total_unrealized_pnl || 0) >= 0 ? 'positive' : 'negative';

        container.innerHTML = `
            <div class="position-overview">
                <div class="position-stat">
                    <div class="stat-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">${data.active_positions || 0}</div>
                        <div class="stat-label">活跃持仓</div>
                    </div>
                </div>
                <div class="position-stat">
                    <div class="stat-icon pnl">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value ${pnlClass}">${Utils.formatCurrency(data.total_unrealized_pnl || 0)}</div>
                        <div class="stat-label">未实现盈亏</div>
                    </div>
                </div>
            </div>
            <div class="position-details">
                <div class="detail-item">
                    <span class="detail-label">总持仓</span>
                    <span class="detail-value">${data.total_positions || 0}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">胜率</span>
                    <span class="detail-value">${Utils.formatPercent(data.win_rate || 0)}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">盈利因子</span>
                    <span class="detail-value">${(data.profit_factor || 0).toFixed(2)}</span>
                </div>
            </div>
        `;

        // 更新性能统计
        this.updatePerformanceStats(data);
    }

    /**
     * 更新性能统计
     * @param {Object} data - 仓位汇总数据
     */
    updatePerformanceStats(data) {
        // 更新总收益
        const totalPnlEl = document.getElementById('total-pnl');
        if (totalPnlEl) {
            const pnl = data.total_unrealized_pnl || 0;
            totalPnlEl.textContent = (pnl >= 0 ? '+' : '') + Utils.formatCurrency(pnl);
            totalPnlEl.className = 'perf-value ' + (pnl >= 0 ? 'positive' : 'negative');
        }

        // 更新胜率
        const winRateEl = document.getElementById('win-rate');
        if (winRateEl) {
            winRateEl.textContent = Utils.formatPercent(data.win_rate || 0);
        }

        // 更新交易次数
        const tradeCountEl = document.getElementById('trade-count');
        if (tradeCountEl) {
            tradeCountEl.textContent = data.total_positions || 0;
        }

        // 更新最大回撤
        const maxDrawdownEl = document.getElementById('max-drawdown');
        if (maxDrawdownEl) {
            maxDrawdownEl.textContent = Utils.formatPercent(data.max_drawdown || 0);
        }
    }

    /**
     * 启动交易
     */
    async startTrading() {
        const startBtn = document.getElementById('start-trading-btn');
        const stopBtn = document.getElementById('stop-trading-btn');
        
        if (startBtn) startBtn.disabled = true;
        
        try {
            const result = await api.startTrading();
            
            if (result.success) {
                Utils.showMessage('交易系统启动成功', 'success');
                this.tradingActive = true;
                
                if (startBtn) startBtn.disabled = true;
                if (stopBtn) stopBtn.disabled = false;
            } else {
                Utils.showMessage('启动交易失败: ' + result.error, 'error');
                if (startBtn) startBtn.disabled = false;
            }
        } catch (error) {
            console.error('启动交易失败:', error);
            Utils.showMessage('启动交易失败', 'error');
            if (startBtn) startBtn.disabled = false;
        }
    }

    /**
     * 停止交易
     */
    async stopTrading() {
        const startBtn = document.getElementById('start-trading-btn');
        const stopBtn = document.getElementById('stop-trading-btn');
        
        if (stopBtn) stopBtn.disabled = true;
        
        try {
            const result = await api.stopTrading();
            
            if (result.success) {
                Utils.showMessage('交易系统已停止', 'success');
                this.tradingActive = false;
                
                if (startBtn) startBtn.disabled = false;
                if (stopBtn) stopBtn.disabled = true;
            } else {
                Utils.showMessage('停止交易失败: ' + result.error, 'error');
                if (stopBtn) stopBtn.disabled = false;
            }
        } catch (error) {
            console.error('停止交易失败:', error);
            Utils.showMessage('停止交易失败', 'error');
            if (stopBtn) stopBtn.disabled = false;
        }
    }

    /**
     * 更新交易页面
     */
    async updateTradingPage() {
        const tradingStatus = this.cache.tradingStatus;
        
        // 更新按钮状态
        const startBtn = document.getElementById('start-trading-btn');
        const stopBtn = document.getElementById('stop-trading-btn');
        
        if (startBtn && stopBtn) {
            if (this.tradingActive) {
                startBtn.disabled = true;
                stopBtn.disabled = false;
            } else {
                startBtn.disabled = false;
                stopBtn.disabled = true;
            }
        }

        // 更新交易状态详情
        const detailsEl = document.getElementById('trading-details');
        if (detailsEl && tradingStatus) {
            if (tradingStatus.trading_active) {
                detailsEl.innerHTML = `
                    <div class="status-item">
                        <span class="label">交易状态:</span>
                        <span class="value success">运行中</span>
                    </div>
                    <div class="status-item">
                        <span class="label">任务调度:</span>
                        <span class="value ${tradingStatus.scheduler_running ? 'success' : 'error'}">
                            ${tradingStatus.scheduler_running ? '运行中' : '已停止'}
                        </span>
                    </div>
                `;
            } else {
                detailsEl.innerHTML = '<p>交易系统未启动</p>';
            }
        }
    }

    /**
     * 更新持仓页面
     */
    async updatePositionsPage() {
        try {
            const result = await api.getPositions();
            if (result.success) {
                this.cache.positions = result.data;
                this.displayPositionsTable(result.data);
            }
        } catch (error) {
            console.error('获取持仓信息失败:', error);
        }
    }

    /**
     * 显示持仓表格
     * @param {Array} positions - 持仓数据
     */
    displayPositionsTable(positions) {
        const tbody = document.querySelector('#positions-table tbody');
        if (!tbody) return;

        if (!positions || positions.length === 0) {
            tbody.innerHTML = '<tr><td colspan="9" class="no-data">暂无持仓数据</td></tr>';
            return;
        }

        tbody.innerHTML = positions.map(position => `
            <tr>
                <td>${position.symbol}</td>
                <td>${position.side}</td>
                <td>${Utils.formatNumber(position.size)}</td>
                <td>${Utils.formatCurrency(position.entry_price)}</td>
                <td>${Utils.formatCurrency(position.current_price)}</td>
                <td class="${position.unrealized_pnl >= 0 ? 'positive' : 'negative'}">
                    ${Utils.formatCurrency(position.unrealized_pnl)}
                </td>
                <td>${position.leverage}x</td>
                <td>${position.status}</td>
                <td>
                    <button class="btn btn-sm btn-danger" onclick="app.closePosition('${position.symbol}')">
                        平仓
                    </button>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 加载配置
     */
    async loadConfig() {
        try {
            const result = await api.getConfig();
            if (result.success && result.data) {
                this.populateConfigForm(result.data);
            }
        } catch (error) {
            console.error('加载配置失败:', error);
        }
    }

    /**
     * 填充配置表单
     * @param {Object} config - 配置数据
     */
    populateConfigForm(config) {
        const tradingParams = config.trading_params || {};
        
        // 填充表单字段
        const fields = {
            'max-leverage': tradingParams.max_leverage || 10,
            'max-position': tradingParams.max_position_ratio || 0.1,
            'stop-loss': tradingParams.stop_loss_ratio || 0.05,
            'take-profit': tradingParams.take_profit_ratio || 0.1,
            'confidence-threshold': tradingParams.confidence_threshold || 70,
            'max-daily-loss': tradingParams.max_daily_loss || 0.1
        };

        Object.entries(fields).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.value = value;
            }
        });
    }

    /**
     * 保存配置
     */
    async saveConfig() {
        try {
            // 收集表单数据
            const config = {
                max_leverage: parseInt(document.getElementById('max-leverage').value),
                max_position_ratio: parseFloat(document.getElementById('max-position').value),
                stop_loss_ratio: parseFloat(document.getElementById('stop-loss').value),
                take_profit_ratio: parseFloat(document.getElementById('take-profit').value),
                confidence_threshold: parseFloat(document.getElementById('confidence-threshold').value),
                max_daily_loss: parseFloat(document.getElementById('max-daily-loss').value)
            };

            // 保存配置
            const result = await api.setConfig('trading_params', config);
            
            if (result.success) {
                Utils.showMessage('配置保存成功', 'success');
            } else {
                Utils.showMessage('保存配置失败: ' + result.error, 'error');
            }
        } catch (error) {
            console.error('保存配置失败:', error);
            Utils.showMessage('保存配置失败', 'error');
        }
    }

    /**
     * 重置配置
     */
    resetConfig() {
        if (confirm('确定要重置配置到默认值吗？')) {
            // 重置为默认值
            document.getElementById('max-leverage').value = 10;
            document.getElementById('max-position').value = 0.1;
            document.getElementById('stop-loss').value = 0.05;
            document.getElementById('take-profit').value = 0.1;
            document.getElementById('confidence-threshold').value = 70;
            document.getElementById('max-daily-loss').value = 0.1;
            
            Utils.showMessage('配置已重置', 'success');
        }
    }

    /**
     * 更新日志页面
     */
    async updateLogsPage() {
        // 这里可以实现日志获取和显示
        const logsContent = document.getElementById('logs-content');
        if (logsContent) {
            logsContent.innerHTML = `
                <div class="log-entry">
                    <span class="timestamp">${Utils.formatTime(new Date())}</span>
                    <span class="level info">INFO</span>
                    <span class="message">系统运行正常</span>
                </div>
            `;
        }
    }

    /**
     * 清空日志
     */
    clearLogs() {
        const logsContent = document.getElementById('logs-content');
        if (logsContent) {
            logsContent.innerHTML = '<p>日志已清空</p>';
            Utils.showMessage('日志已清空', 'success');
        }
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.stopDataUpdate();
        chartManager.destroyAllCharts();
    }
}

// 创建全局应用实例
let app;

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    app = new SuperBotApp();
});

// 导出到全局
window.app = app;
