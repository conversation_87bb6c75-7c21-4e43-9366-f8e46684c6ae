/**
 * 工具函数库
 * 提供通用的工具函数和API调用封装
 */

// API调用封装
class APIClient {
    constructor() {
        this.baseURL = '';
        this.timeout = 10000; // 10秒超时
    }

    /**
     * 调用Python API
     * @param {string} method - API方法名
     * @param {*} params - 参数
     * @returns {Promise} API响应
     */
    async call(method, params = null) {
        try {
            // 检查pywebview是否可用
            if (typeof pywebview === 'undefined' || !pywebview.api) {
                throw new Error('PyWebview API不可用');
            }

            // 调用Python方法
            const result = await pywebview.api[method](params);
            
            // 检查响应格式
            if (typeof result === 'object' && result.hasOwnProperty('success')) {
                return result;
            } else {
                // 兼容旧格式
                return {
                    success: true,
                    data: result,
                    timestamp: new Date().toISOString()
                };
            }
        } catch (error) {
            console.error(`API调用失败 [${method}]:`, error);
            return {
                success: false,
                error: error.message || '未知错误',
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 获取系统状态
     */
    async getSystemStatus() {
        return await this.call('get_system_status');
    }

    /**
     * 获取系统信息
     */
    async getSystemInfo() {
        return await this.call('get_system_info');
    }

    /**
     * 启动交易
     */
    async startTrading(pairs = null) {
        return await this.call('start_trading', pairs);
    }

    /**
     * 停止交易
     */
    async stopTrading() {
        return await this.call('stop_trading');
    }

    /**
     * 获取交易状态
     */
    async getTradingStatus() {
        return await this.call('get_trading_status');
    }

    /**
     * 获取持仓信息
     */
    async getPositions() {
        return await this.call('get_positions');
    }

    /**
     * 获取仓位汇总
     */
    async getPositionSummary() {
        return await this.call('get_position_summary');
    }

    /**
     * 获取市场数据
     */
    async getMarketData(symbol = 'BTC/USDT:USDT') {
        return await this.call('get_market_data', symbol);
    }

    /**
     * 获取账户余额
     */
    async getAccountBalance() {
        return await this.call('get_account_balance');
    }

    /**
     * 获取任务状态
     */
    async getTaskStatus() {
        return await this.call('get_task_status');
    }

    /**
     * 获取配置
     */
    async getConfig(key = null) {
        return await this.call('get_config', key);
    }

    /**
     * 设置配置
     */
    async setConfig(key, value) {
        return await this.call('set_config', { key, value });
    }

    /**
     * 获取交易对列表
     */
    async getTradingPairs() {
        return await this.call('get_trading_pairs');
    }

    /**
     * API连通性测试
     */
    async ping() {
        return await this.call('ping');
    }

    /**
     * 获取API统计
     */
    async getAPIStats() {
        return await this.call('get_api_stats');
    }
}

// 全局API客户端实例
const api = new APIClient();

// 工具函数
const Utils = {
    /**
     * 格式化数字
     * @param {number} num - 数字
     * @param {number} decimals - 小数位数
     * @returns {string} 格式化后的数字
     */
    formatNumber(num, decimals = 2) {
        if (typeof num !== 'number' || isNaN(num)) {
            return '0.00';
        }
        return num.toFixed(decimals);
    },

    /**
     * 格式化百分比
     * @param {number} num - 数字
     * @param {number} decimals - 小数位数
     * @returns {string} 格式化后的百分比
     */
    formatPercent(num, decimals = 2) {
        if (typeof num !== 'number' || isNaN(num)) {
            return '0.00%';
        }
        return (num * 100).toFixed(decimals) + '%';
    },

    /**
     * 格式化货币
     * @param {number} amount - 金额
     * @param {string} currency - 货币符号
     * @returns {string} 格式化后的货币
     */
    formatCurrency(amount, currency = 'USDT') {
        if (typeof amount !== 'number' || isNaN(amount)) {
            return '0.00 ' + currency;
        }
        return amount.toFixed(2) + ' ' + currency;
    },

    /**
     * 格式化时间
     * @param {string|Date} timestamp - 时间戳
     * @returns {string} 格式化后的时间
     */
    formatTime(timestamp) {
        try {
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        } catch (error) {
            return '无效时间';
        }
    },

    /**
     * 格式化相对时间
     * @param {string|Date} timestamp - 时间戳
     * @returns {string} 相对时间
     */
    formatRelativeTime(timestamp) {
        try {
            const date = new Date(timestamp);
            const now = new Date();
            const diff = now - date;
            
            const seconds = Math.floor(diff / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);
            
            if (days > 0) return `${days}天前`;
            if (hours > 0) return `${hours}小时前`;
            if (minutes > 0) return `${minutes}分钟前`;
            return `${seconds}秒前`;
        } catch (error) {
            return '未知时间';
        }
    },

    /**
     * 显示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, error, warning)
     * @param {number} duration - 显示时长(毫秒)
     */
    showMessage(message, type = 'success', duration = 3000) {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `message ${type}`;
        messageEl.textContent = message;
        
        // 添加到页面
        document.body.appendChild(messageEl);
        
        // 设置样式
        messageEl.style.position = 'fixed';
        messageEl.style.top = '80px';
        messageEl.style.right = '20px';
        messageEl.style.zIndex = '9999';
        messageEl.style.minWidth = '300px';
        messageEl.style.maxWidth = '500px';
        messageEl.style.opacity = '0';
        messageEl.style.transform = 'translateX(100%)';
        messageEl.style.transition = 'all 0.3s ease';
        
        // 显示动画
        setTimeout(() => {
            messageEl.style.opacity = '1';
            messageEl.style.transform = 'translateX(0)';
        }, 10);
        
        // 自动隐藏
        setTimeout(() => {
            messageEl.style.opacity = '0';
            messageEl.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, 300);
        }, duration);
    },

    /**
     * 显示加载状态
     * @param {HTMLElement} element - 目标元素
     * @param {boolean} loading - 是否加载中
     */
    setLoading(element, loading = true) {
        if (loading) {
            element.innerHTML = '<div class="loading"><div class="loading-spinner"></div><p>加载中...</p></div>';
        }
    },

    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间
     * @returns {Function} 防抖后的函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} limit - 限制时间
     * @returns {Function} 节流后的函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    /**
     * 深拷贝对象
     * @param {*} obj - 要拷贝的对象
     * @returns {*} 拷贝后的对象
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }
        
        if (obj instanceof Date) {
            return new Date(obj.getTime());
        }
        
        if (obj instanceof Array) {
            return obj.map(item => this.deepClone(item));
        }
        
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },

    /**
     * 生成随机ID
     * @param {number} length - ID长度
     * @returns {string} 随机ID
     */
    generateId(length = 8) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    },

    /**
     * 检查是否为移动设备
     * @returns {boolean} 是否为移动设备
     */
    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },

    /**
     * 获取URL参数
     * @param {string} name - 参数名
     * @returns {string|null} 参数值
     */
    getUrlParam(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    },

    /**
     * 本地存储封装
     */
    storage: {
        set(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (error) {
                console.error('存储数据失败:', error);
                return false;
            }
        },

        get(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (error) {
                console.error('读取数据失败:', error);
                return defaultValue;
            }
        },

        remove(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (error) {
                console.error('删除数据失败:', error);
                return false;
            }
        },

        clear() {
            try {
                localStorage.clear();
                return true;
            } catch (error) {
                console.error('清空数据失败:', error);
                return false;
            }
        }
    }
};

// 导出到全局
window.api = api;
window.Utils = Utils;
