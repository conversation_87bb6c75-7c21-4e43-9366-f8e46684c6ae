<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SuperBot - 加密货币量化交易系统</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-robot"></i>
                <span>SuperBot</span>
            </div>
            <div class="nav-status">
                <span id="connection-status" class="status-indicator">
                    <i class="fas fa-circle"></i>
                    <span>连接中...</span>
                </span>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="main-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-menu">
                <div class="menu-item active" data-tab="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item" data-tab="trading">
                    <i class="fas fa-chart-line"></i>
                    <span>交易控制</span>
                </div>
                <div class="menu-item" data-tab="positions">
                    <i class="fas fa-wallet"></i>
                    <span>持仓管理</span>
                </div>
                <div class="menu-item" data-tab="config">
                    <i class="fas fa-cog"></i>
                    <span>系统配置</span>
                </div>
                <div class="menu-item" data-tab="logs">
                    <i class="fas fa-list-alt"></i>
                    <span>日志记录</span>
                </div>
            </div>
        </aside>

        <!-- 内容区域 -->
        <main class="content">
            <!-- 仪表盘页面 -->
            <div id="dashboard" class="tab-content active">
                <!-- 仪表盘头部 -->
                <div class="dashboard-header">
                    <div class="header-left">
                        <h1><i class="fas fa-tachometer-alt"></i> 交易仪表盘</h1>
                        <p class="subtitle">实时监控您的量化交易系统</p>
                    </div>
                    <div class="header-right">
                        <div class="last-update">
                            <i class="fas fa-sync-alt"></i>
                            <span id="last-update-time">刚刚更新</span>
                        </div>
                        <div class="connection-status" id="connection-status">
                            <i class="fas fa-circle"></i>
                            <span>已连接</span>
                        </div>
                    </div>
                </div>

                <!-- 主要指标卡片 -->
                <div class="main-metrics">
                    <!-- 账户资产卡片 -->
                    <div class="metric-card asset-overview">
                        <div class="card-header">
                            <h3><i class="fas fa-wallet"></i> 账户资产</h3>
                            <div class="card-actions">
                                <button class="btn-icon" title="刷新"><i class="fas fa-refresh"></i></button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="asset-grid">
                                <div class="asset-item primary">
                                    <div class="asset-label">总资产</div>
                                    <div class="asset-value" id="total-balance">加载中...</div>
                                    <div class="asset-unit">USDT</div>
                                </div>
                                <div class="asset-item">
                                    <div class="asset-label">可用余额</div>
                                    <div class="asset-value" id="available-balance">加载中...</div>
                                    <div class="asset-unit">USDT</div>
                                </div>
                                <div class="asset-item">
                                    <div class="asset-label">已用保证金</div>
                                    <div class="asset-value" id="used-margin">加载中...</div>
                                    <div class="asset-unit">USDT</div>
                                </div>
                                <div class="asset-item">
                                    <div class="asset-label">保证金率</div>
                                    <div class="asset-value" id="margin-ratio">0%</div>
                                    <div class="asset-unit"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 交易状态卡片 -->
                    <div class="metric-card trading-status">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-line"></i> 交易状态</h3>
                            <div class="status-indicator" id="trading-indicator">
                                <span class="status-dot"></span>
                                <span id="trading-status-text">未启动</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="status-grid">
                                <div class="status-item">
                                    <div class="status-icon system">
                                        <i class="fas fa-server"></i>
                                    </div>
                                    <div class="status-info">
                                        <div class="status-label">系统状态</div>
                                        <div class="status-value" id="system-status-text">初始化中...</div>
                                    </div>
                                </div>
                                <div class="status-item">
                                    <div class="status-icon scheduler">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="status-info">
                                        <div class="status-label">任务调度</div>
                                        <div class="status-value" id="scheduler-status-text">未运行</div>
                                    </div>
                                </div>
                                <div class="status-item">
                                    <div class="status-icon positions">
                                        <i class="fas fa-briefcase"></i>
                                    </div>
                                    <div class="status-info">
                                        <div class="status-label">持仓数量</div>
                                        <div class="status-value" id="positions-count">0</div>
                                    </div>
                                </div>
                                <div class="status-item">
                                    <div class="status-icon ai">
                                        <i class="fas fa-brain"></i>
                                    </div>
                                    <div class="status-info">
                                        <div class="status-label">AI引擎</div>
                                        <div class="status-value" id="ai-status-text">就绪</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据面板 -->
                <div class="data-panels">
                    <!-- 市场数据面板 -->
                    <div class="data-panel market-panel">
                        <div class="panel-header">
                            <h3><i class="fas fa-chart-area"></i> 市场数据</h3>
                            <div class="panel-controls">
                                <select id="market-symbol" class="symbol-selector">
                                    <option value="BTC/USDT:USDT">BTC/USDT</option>
                                    <option value="ETH/USDT:USDT">ETH/USDT</option>
                                </select>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div id="market-data" class="market-data-grid">
                                <div class="market-loading">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    <span>正在加载市场数据...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 持仓汇总面板 -->
                    <div class="data-panel position-panel">
                        <div class="panel-header">
                            <h3><i class="fas fa-pie-chart"></i> 持仓汇总</h3>
                            <div class="panel-controls">
                                <button class="btn-small" onclick="app.updatePositionSummary()">
                                    <i class="fas fa-sync"></i> 刷新
                                </button>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div id="position-summary" class="position-summary-grid">
                                <div class="position-loading">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    <span>正在加载持仓数据...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 性能统计面板 -->
                    <div class="data-panel performance-panel">
                        <div class="panel-header">
                            <h3><i class="fas fa-trophy"></i> 交易统计</h3>
                            <div class="panel-controls">
                                <select id="stats-period" class="period-selector">
                                    <option value="today">今日</option>
                                    <option value="week">本周</option>
                                    <option value="month">本月</option>
                                </select>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div id="performance-stats" class="performance-grid">
                                <div class="perf-item">
                                    <div class="perf-label">总收益</div>
                                    <div class="perf-value positive" id="total-pnl">+0.00</div>
                                    <div class="perf-unit">USDT</div>
                                </div>
                                <div class="perf-item">
                                    <div class="perf-label">胜率</div>
                                    <div class="perf-value" id="win-rate">0%</div>
                                    <div class="perf-unit"></div>
                                </div>
                                <div class="perf-item">
                                    <div class="perf-label">交易次数</div>
                                    <div class="perf-value" id="trade-count">0</div>
                                    <div class="perf-unit">笔</div>
                                </div>
                                <div class="perf-item">
                                    <div class="perf-label">最大回撤</div>
                                    <div class="perf-value" id="max-drawdown">0%</div>
                                    <div class="perf-unit"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 交易控制页面 -->
            <div id="trading" class="tab-content">
                <div class="page-header">
                    <h1>交易控制</h1>
                    <p>启动、停止和监控交易系统</p>
                </div>

                <div class="control-panel">
                    <div class="control-section">
                        <h3>交易控制</h3>
                        <div class="control-buttons">
                            <button id="start-trading-btn" class="btn btn-success">
                                <i class="fas fa-play"></i>
                                启动交易
                            </button>
                            <button id="stop-trading-btn" class="btn btn-danger" disabled>
                                <i class="fas fa-stop"></i>
                                停止交易
                            </button>
                        </div>
                    </div>

                    <div class="control-section">
                        <h3>交易对选择</h3>
                        <div class="trading-pairs">
                            <div class="pair-item">
                                <input type="checkbox" id="btc-usdt" checked>
                                <label for="btc-usdt">BTC/USDT</label>
                            </div>
                            <div class="pair-item">
                                <input type="checkbox" id="eth-usdt" checked>
                                <label for="eth-usdt">ETH/USDT</label>
                            </div>
                            <div class="pair-item">
                                <input type="checkbox" id="bnb-usdt">
                                <label for="bnb-usdt">BNB/USDT</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="trading-status">
                    <h3>交易状态详情</h3>
                    <div id="trading-details">
                        <p>交易系统未启动</p>
                    </div>
                </div>
            </div>

            <!-- 持仓管理页面 -->
            <div id="positions" class="tab-content">
                <div class="page-header">
                    <h1>持仓管理</h1>
                    <p>查看和管理当前持仓</p>
                </div>

                <div class="positions-table">
                    <table id="positions-table">
                        <thead>
                            <tr>
                                <th>交易对</th>
                                <th>方向</th>
                                <th>数量</th>
                                <th>开仓价</th>
                                <th>当前价</th>
                                <th>盈亏</th>
                                <th>杠杆</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="9" class="no-data">暂无持仓数据</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 系统配置页面 -->
            <div id="config" class="tab-content">
                <div class="page-header">
                    <h1>系统配置</h1>
                    <p>配置交易参数和系统设置</p>
                </div>

                <div class="config-sections">
                    <div class="config-section">
                        <h3>交易参数</h3>
                        <div class="config-form">
                            <div class="form-group">
                                <label for="max-leverage">最大杠杆</label>
                                <input type="number" id="max-leverage" min="1" max="100" value="10">
                            </div>
                            <div class="form-group">
                                <label for="max-position">最大仓位比例</label>
                                <input type="number" id="max-position" min="0.01" max="1" step="0.01" value="0.1">
                            </div>
                            <div class="form-group">
                                <label for="stop-loss">止损比例</label>
                                <input type="number" id="stop-loss" min="0.01" max="0.5" step="0.01" value="0.05">
                            </div>
                            <div class="form-group">
                                <label for="take-profit">止盈比例</label>
                                <input type="number" id="take-profit" min="0.01" max="1" step="0.01" value="0.1">
                            </div>
                        </div>
                    </div>

                    <div class="config-section">
                        <h3>风险控制</h3>
                        <div class="config-form">
                            <div class="form-group">
                                <label for="confidence-threshold">置信度阈值</label>
                                <input type="number" id="confidence-threshold" min="50" max="95" value="70">
                            </div>
                            <div class="form-group">
                                <label for="max-daily-loss">最大日损失</label>
                                <input type="number" id="max-daily-loss" min="0.01" max="0.5" step="0.01" value="0.1">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="config-actions">
                    <button id="save-config-btn" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        保存配置
                    </button>
                    <button id="reset-config-btn" class="btn btn-secondary">
                        <i class="fas fa-undo"></i>
                        重置配置
                    </button>
                </div>
            </div>

            <!-- 日志记录页面 -->
            <div id="logs" class="tab-content">
                <div class="page-header">
                    <h1>日志记录</h1>
                    <p>查看系统运行日志和任务状态</p>
                </div>

                <div class="logs-section">
                    <div class="logs-controls">
                        <select id="log-level">
                            <option value="all">所有级别</option>
                            <option value="info">信息</option>
                            <option value="warning">警告</option>
                            <option value="error">错误</option>
                        </select>
                        <button id="clear-logs-btn" class="btn btn-secondary">
                            <i class="fas fa-trash"></i>
                            清空日志
                        </button>
                    </div>
                    <div class="logs-container">
                        <div id="logs-content">
                            <p>正在加载日志...</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 加载脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/charts.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
