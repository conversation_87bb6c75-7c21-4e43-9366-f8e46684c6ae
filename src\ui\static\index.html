<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SuperBot - 加密货币量化交易系统</title>

    <!-- Ant Design Vue CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/ant-design-vue@4.0.8/dist/reset.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/ant-design-vue@4.0.8/dist/antd.min.css">

    <!-- 自定义样式 -->
    <link rel="stylesheet" href="css/style.css">

    <!-- Vue 3 -->
    <script src="https://cdn.jsdelivr.net/npm/vue@3.3.8/dist/vue.global.js"></script>
    <!-- Day.js for date handling -->
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/dayjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/plugin/relativeTime.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/locale/zh-cn.js"></script>
    <!-- Ant Design Vue -->
    <script src="https://cdn.jsdelivr.net/npm/ant-design-vue@4.0.8/dist/antd.min.js"></script>
</head>
<body>
    <div id="app">
        <a-config-provider :locale="zhCN">
            <a-layout class="main-layout">
                <!-- 顶部导航栏 -->
                <a-layout-header class="header">
                    <div class="header-content">
                        <!-- 品牌Logo -->
                        <div class="brand">
                            <div class="brand-icon">
                                <a-avatar :size="40" style="background-color: #1890ff;">
                                    🤖
                                </a-avatar>
                            </div>
                            <div class="brand-text">
                                <h1>SuperBot</h1>
                                <span>量化交易系统</span>
                            </div>
                        </div>

                        <!-- 头部操作区 -->
                        <div class="header-actions">
                            <!-- 环境切换 -->
                            <div class="env-switch">
                                <a-space>
                                    <span :style="{ color: !isLiveMode ? '#1890ff' : '#8c8c8c' }">模拟盘</span>
                                    <a-switch
                                        v-model:checked="isLiveMode"
                                        @change="handleEnvChange"
                                        :loading="envSwitching"
                                    />
                                    <span :style="{ color: isLiveMode ? '#1890ff' : '#8c8c8c' }">实盘</span>
                                </a-space>
                            </div>

                            <!-- 连接状态 -->
                            <a-badge :status="connectionStatus" :text="connectionText" />

                            <!-- 系统状态 -->
                            <a-dropdown>
                                <a-button type="text" class="status-btn">
                                    📊 系统状态 ▼
                                </a-button>
                                <template #overlay>
                                    <a-menu>
                                        <a-menu-item>
                                            <a-space>
                                                <a-badge :status="systemStatus.api" />
                                                <span>API连接</span>
                                            </a-space>
                                        </a-menu-item>
                                        <a-menu-item>
                                            <a-space>
                                                <a-badge :status="systemStatus.ai" />
                                                <span>AI引擎</span>
                                            </a-space>
                                        </a-menu-item>
                                        <a-menu-item>
                                            <a-space>
                                                <a-badge :status="systemStatus.trading" />
                                                <span>交易系统</span>
                                            </a-space>
                                        </a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>

                            <!-- 用户操作 -->
                            <a-space>
                                <a-tooltip title="刷新数据">
                                    <a-button
                                        type="text"
                                        :loading="refreshing"
                                        @click="refreshData"
                                    >
                                        🔄
                                    </a-button>
                                </a-tooltip>
                                <a-tooltip title="系统设置">
                                    <a-button
                                        type="text"
                                        @click="showSettings = true"
                                    >
                                        ⚙️
                                    </a-button>
                                </a-tooltip>
                            </a-space>
                        </div>
                    </div>
                </a-layout-header>

                <!-- 主要内容区域 -->
                <a-layout>
                    <!-- 侧边栏 -->
                    <a-layout-sider
                        v-model:collapsed="siderCollapsed"
                        :trigger="null"
                        collapsible
                        width="240"
                        class="sider"
                    >
                        <div class="sider-content">
                            <!-- 折叠按钮 -->
                            <div class="sider-trigger">
                                <a-button
                                    type="text"
                                    @click="siderCollapsed = !siderCollapsed"
                                >
                                    {{ siderCollapsed ? '▶' : '◀' }}
                                </a-button>
                            </div>

                            <!-- 导航菜单 -->
                            <a-menu
                                v-model:selectedKeys="selectedMenuKeys"
                                mode="inline"
                                :inline-collapsed="siderCollapsed"
                                @click="handleMenuClick"
                            >
                                <a-menu-item key="dashboard">
                                    📊 <span>仪表盘</span>
                                </a-menu-item>
                                <a-menu-item key="trading">
                                    📈 <span>交易控制</span>
                                </a-menu-item>
                                <a-menu-item key="positions">
                                    💼 <span>持仓管理</span>
                                </a-menu-item>
                                <a-menu-item key="analysis">
                                    📊 <span>市场分析</span>
                                </a-menu-item>
                                <a-menu-item key="history">
                                    📋 <span>交易历史</span>
                                </a-menu-item>
                                <a-menu-item key="logs">
                                    📝 <span>系统日志</span>
                                </a-menu-item>
                            </a-menu>
                        </div>
                    </a-layout-sider>

                    <!-- 内容区域 -->
                    <a-layout-content class="content">
                        <div class="content-wrapper">
                            <!-- 仪表盘页面 -->
                            <div v-show="currentTab === 'dashboard'" class="page-content">
                                <!-- 页面头部 -->
                                <div class="page-header">
                                    <div class="page-header-content">
                                        <div class="page-title">
                                            <h2>交易仪表盘</h2>
                                            <p>实时监控您的量化交易系统</p>
                                        </div>
                                        <div class="page-actions">
                                            <a-space>
                                                <a-button
                                                    type="primary"
                                                    :loading="tradingToggling"
                                                    @click="toggleTrading"
                                                    v-if="!isTrading"
                                                >
                                                    ▶️ 启动交易
                                                </a-button>
                                                <a-button
                                                    danger
                                                    :loading="tradingToggling"
                                                    @click="toggleTrading"
                                                    v-else
                                                >
                                                    ⏸️ 停止交易
                                                </a-button>
                                                <a-button
                                                    :loading="refreshing"
                                                    @click="refreshData"
                                                >
                                                    🔄 刷新
                                                </a-button>
                                            </a-space>
                                        </div>
                                    </div>
                                </div>

                                <!-- 核心指标卡片 -->
                                <div class="metrics-section">
                                    <a-row :gutter="[16, 16]">
                                        <!-- 账户资产卡片 -->
                                        <a-col :xs="24" :sm="12" :lg="6">
                                            <a-card class="metric-card asset-card" :loading="loading.balance">
                                                <a-statistic
                                                    title="总资产"
                                                    :value="accountData.totalBalance"
                                                    suffix="USDT"
                                                    :precision="2"
                                                    :value-style="{ color: '#1890ff', fontSize: '24px' }"
                                                />
                                                <div class="metric-change">
                                                    <a-statistic
                                                        :value="accountData.balanceChange"
                                                        :precision="2"
                                                        suffix="%"
                                                        :value-style="{
                                                            color: accountData.balanceChange >= 0 ? '#3f8600' : '#cf1322',
                                                            fontSize: '14px'
                                                        }"
                                                        :prefix="accountData.balanceChange >= 0 ? '↗' : '↘'"
                                                    />
                                                    <span class="change-label">24h变化</span>
                                                </div>
                                            </a-card>
                                        </a-col>

                                        <!-- 今日盈亏卡片 -->
                                        <a-col :xs="24" :sm="12" :lg="6">
                                            <a-card class="metric-card pnl-card" :loading="loading.pnl">
                                                <a-statistic
                                                    title="今日盈亏"
                                                    :value="tradingData.todayPnl"
                                                    suffix="USDT"
                                                    :precision="2"
                                                    :value-style="{
                                                        color: tradingData.todayPnl >= 0 ? '#3f8600' : '#cf1322',
                                                        fontSize: '24px'
                                                    }"
                                                    :prefix="tradingData.todayPnl >= 0 ? '+' : ''"
                                                />
                                                <div class="metric-extra">
                                                    <a-tag :color="tradingData.todayPnl >= 0 ? 'green' : 'red'">
                                                        {{ tradingData.todayPnlPercent >= 0 ? '+' : '' }}{{ tradingData.todayPnlPercent.toFixed(2) }}%
                                                    </a-tag>
                                                </div>
                                            </a-card>
                                        </a-col>

                                        <!-- 持仓数量卡片 -->
                                        <a-col :xs="24" :sm="12" :lg="6">
                                            <a-card class="metric-card position-card" :loading="loading.positions">
                                                <a-statistic
                                                    title="当前持仓"
                                                    :value="positionData.count"
                                                    suffix="个"
                                                    :value-style="{ color: '#722ed1', fontSize: '24px' }"
                                                />
                                                <div class="metric-extra">
                                                    <a-space>
                                                        <a-tag color="blue">多头: {{ positionData.longCount }}</a-tag>
                                                        <a-tag color="orange">空头: {{ positionData.shortCount }}</a-tag>
                                                    </a-space>
                                                </div>
                                            </a-card>
                                        </a-col>

                                        <!-- AI引擎状态卡片 -->
                                        <a-col :xs="24" :sm="12" :lg="6">
                                            <a-card class="metric-card ai-card">
                                                <div class="ai-status-header">
                                                    <h4>AI引擎</h4>
                                                    <a-badge :status="aiData.status" :text="aiData.statusText" />
                                                </div>
                                                <div class="ai-metrics">
                                                    <div class="ai-metric-item">
                                                        <span class="label">信号置信度</span>
                                                        <a-progress
                                                            :percent="aiData.confidence"
                                                            size="small"
                                                            :stroke-color="aiData.confidence > 70 ? '#52c41a' : '#faad14'"
                                                        />
                                                    </div>
                                                    <div class="ai-metric-item">
                                                        <span class="label">上次分析</span>
                                                        <span class="value">{{ aiData.lastAnalysis }}</span>
                                                    </div>
                                                </div>
                                            </a-card>
                                        </a-col>
                                    </a-row>
                                </div>

                                <!-- 详细数据面板 -->
                                <div class="data-section">
                                    <a-row :gutter="[16, 16]">
                                        <!-- 市场数据面板 -->
                                        <a-col :xs="24" :lg="12">
                                            <a-card title="市场数据" class="data-panel">
                                                <template #extra>
                                                    <a-select
                                                        v-model:value="selectedSymbol"
                                                        style="width: 120px"
                                                        @change="handleSymbolChange"
                                                    >
                                                        <a-select-option value="BTC/USDT:USDT">BTC/USDT</a-select-option>
                                                        <a-select-option value="ETH/USDT:USDT">ETH/USDT</a-select-option>
                                                        <a-select-option value="BNB/USDT:USDT">BNB/USDT</a-select-option>
                                                    </a-select>
                                                </template>
                                                <div class="market-data-content">
                                                    <a-row :gutter="16">
                                                        <a-col :span="12">
                                                            <a-statistic
                                                                title="当前价格"
                                                                :value="marketData.price"
                                                                :precision="2"
                                                                suffix="USDT"
                                                                :value-style="{ fontSize: '20px' }"
                                                            />
                                                        </a-col>
                                                        <a-col :span="12">
                                                            <a-statistic
                                                                title="24h涨跌"
                                                                :value="marketData.change24h"
                                                                :precision="2"
                                                                suffix="%"
                                                                :value-style="{
                                                                    color: marketData.change24h >= 0 ? '#3f8600' : '#cf1322',
                                                                    fontSize: '20px'
                                                                }"
                                                                :prefix="marketData.change24h >= 0 ? '+' : ''"
                                                            />
                                                        </a-col>
                                                    </a-row>
                                                    <a-divider />
                                                    <div class="market-details">
                                                        <a-descriptions :column="2" size="small">
                                                            <a-descriptions-item label="24h最高">{{ marketData.high24h }}</a-descriptions-item>
                                                            <a-descriptions-item label="24h最低">{{ marketData.low24h }}</a-descriptions-item>
                                                            <a-descriptions-item label="24h成交量">{{ marketData.volume24h }}</a-descriptions-item>
                                                            <a-descriptions-item label="持仓量">{{ marketData.openInterest }}</a-descriptions-item>
                                                        </a-descriptions>
                                                    </div>
                                                </div>
                                            </a-card>
                                        </a-col>

                                        <!-- 持仓汇总面板 -->
                                        <a-col :xs="24" :lg="12">
                                            <a-card title="持仓汇总" class="data-panel">
                                                <template #extra>
                                                    <a-button
                                                        size="small"
                                                        :icon="h(ReloadOutlined)"
                                                        :loading="loading.positions"
                                                        @click="refreshPositions"
                                                    >
                                                        刷新
                                                    </a-button>
                                                </template>
                                                <div class="position-summary">
                                                    <a-empty v-if="positionData.positions.length === 0" description="暂无持仓" />
                                                    <div v-else>
                                                        <div class="position-overview">
                                                            <a-row :gutter="16">
                                                                <a-col :span="8">
                                                                    <a-statistic
                                                                        title="总持仓价值"
                                                                        :value="positionData.totalValue"
                                                                        suffix="USDT"
                                                                        :precision="2"
                                                                    />
                                                                </a-col>
                                                                <a-col :span="8">
                                                                    <a-statistic
                                                                        title="未实现盈亏"
                                                                        :value="positionData.unrealizedPnl"
                                                                        suffix="USDT"
                                                                        :precision="2"
                                                                        :value-style="{
                                                                            color: positionData.unrealizedPnl >= 0 ? '#3f8600' : '#cf1322'
                                                                        }"
                                                                        :prefix="positionData.unrealizedPnl >= 0 ? '+' : ''"
                                                                    />
                                                                </a-col>
                                                                <a-col :span="8">
                                                                    <a-statistic
                                                                        title="保证金占用"
                                                                        :value="positionData.marginUsed"
                                                                        suffix="USDT"
                                                                        :precision="2"
                                                                    />
                                                                </a-col>
                                                            </a-row>
                                                        </div>
                                                        <a-divider />
                                                        <div class="position-list">
                                                            <a-list
                                                                :data-source="positionData.positions"
                                                                size="small"
                                                            >
                                                                <template #renderItem="{ item }">
                                                                    <a-list-item>
                                                                        <a-list-item-meta>
                                                                            <template #title>
                                                                                <a-space>
                                                                                    <span>{{ item.symbol }}</span>
                                                                                    <a-tag :color="item.side === 'long' ? 'green' : 'red'">
                                                                                        {{ item.side === 'long' ? '多头' : '空头' }}
                                                                                    </a-tag>
                                                                                </a-space>
                                                                            </template>
                                                                            <template #description>
                                                                                数量: {{ item.size }} | 开仓价: {{ item.entryPrice }}
                                                                            </template>
                                                                        </a-list-item-meta>
                                                                        <div class="position-pnl">
                                                                            <a-statistic
                                                                                :value="item.pnl"
                                                                                :precision="2"
                                                                                suffix="USDT"
                                                                                :value-style="{
                                                                                    color: item.pnl >= 0 ? '#3f8600' : '#cf1322',
                                                                                    fontSize: '14px'
                                                                                }"
                                                                                :prefix="item.pnl >= 0 ? '+' : ''"
                                                                            />
                                                                        </div>
                                                                    </a-list-item>
                                                                </template>
                                                            </a-list>
                                                        </div>
                                                    </div>
                                                </div>
                                            </a-card>
                                        </a-col>
                                    </a-row>
                                </div>
                            </div>

                <!-- 数据面板 -->
                <div class="data-panels">
                    <!-- 市场数据面板 -->
                    <div class="data-panel market-panel">
                        <div class="panel-header">
                            <h3><i class="fas fa-chart-area"></i> 市场数据</h3>
                            <div class="panel-controls">
                                <select id="market-symbol" class="symbol-selector">
                                    <option value="BTC/USDT:USDT">BTC/USDT</option>
                                    <option value="ETH/USDT:USDT">ETH/USDT</option>
                                </select>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div id="market-data" class="market-data-grid">
                                <div class="market-loading">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    <span>正在加载市场数据...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 持仓汇总面板 -->
                    <div class="data-panel position-panel">
                        <div class="panel-header">
                            <h3><i class="fas fa-pie-chart"></i> 持仓汇总</h3>
                            <div class="panel-controls">
                                <button class="btn-small" onclick="app.updatePositionSummary()">
                                    <i class="fas fa-sync"></i> 刷新
                                </button>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div id="position-summary" class="position-summary-grid">
                                <div class="position-loading">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    <span>正在加载持仓数据...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 性能统计面板 -->
                    <div class="data-panel performance-panel">
                        <div class="panel-header">
                            <h3><i class="fas fa-trophy"></i> 交易统计</h3>
                            <div class="panel-controls">
                                <select id="stats-period" class="period-selector">
                                    <option value="today">今日</option>
                                    <option value="week">本周</option>
                                    <option value="month">本月</option>
                                </select>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div id="performance-stats" class="performance-grid">
                                <div class="perf-item">
                                    <div class="perf-label">总收益</div>
                                    <div class="perf-value positive" id="total-pnl">+0.00</div>
                                    <div class="perf-unit">USDT</div>
                                </div>
                                <div class="perf-item">
                                    <div class="perf-label">胜率</div>
                                    <div class="perf-value" id="win-rate">0%</div>
                                    <div class="perf-unit"></div>
                                </div>
                                <div class="perf-item">
                                    <div class="perf-label">交易次数</div>
                                    <div class="perf-value" id="trade-count">0</div>
                                    <div class="perf-unit">笔</div>
                                </div>
                                <div class="perf-item">
                                    <div class="perf-label">最大回撤</div>
                                    <div class="perf-value" id="max-drawdown">0%</div>
                                    <div class="perf-unit"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

                            <!-- 交易控制页面 -->
                            <div v-show="currentTab === 'trading'" class="page-content">
                                <div class="page-header">
                                    <div class="page-header-content">
                                        <div class="page-title">
                                            <h2>交易控制</h2>
                                            <p>启动、停止和监控交易系统</p>
                                        </div>
                                    </div>
                                </div>

                                <a-row :gutter="[16, 16]">
                                    <!-- 交易控制面板 -->
                                    <a-col :xs="24" :lg="12">
                                        <a-card title="交易控制" class="control-card">
                                            <div class="trading-controls">
                                                <div class="control-section">
                                                    <h4>系统状态</h4>
                                                    <a-space direction="vertical" style="width: 100%">
                                                        <a-alert
                                                            :type="isTrading ? 'success' : 'info'"
                                                            :message="isTrading ? '交易系统运行中' : '交易系统已停止'"
                                                            :description="isTrading ? '系统正在自动执行交易策略' : '点击启动按钮开始自动交易'"
                                                            show-icon
                                                        />
                                                        <div class="control-buttons">
                                                            <a-button
                                                                type="primary"
                                                                size="large"
                                                                :icon="h(PlayCircleOutlined)"
                                                                :loading="tradingToggling"
                                                                @click="startTrading"
                                                                v-if="!isTrading"
                                                                block
                                                            >
                                                                启动交易系统
                                                            </a-button>
                                                            <a-button
                                                                danger
                                                                size="large"
                                                                :icon="h(PauseCircleOutlined)"
                                                                :loading="tradingToggling"
                                                                @click="stopTrading"
                                                                v-else
                                                                block
                                                            >
                                                                停止交易系统
                                                            </a-button>
                                                        </div>
                                                    </a-space>
                                                </div>

                                                <a-divider />

                                                <div class="control-section">
                                                    <h4>交易对配置</h4>
                                                    <a-checkbox-group
                                                        v-model:value="selectedPairs"
                                                        @change="handlePairsChange"
                                                    >
                                                        <a-row :gutter="[16, 8]">
                                                            <a-col :span="8" v-for="pair in availablePairs" :key="pair.value">
                                                                <a-checkbox :value="pair.value">
                                                                    {{ pair.label }}
                                                                </a-checkbox>
                                                            </a-col>
                                                        </a-row>
                                                    </a-checkbox-group>
                                                </div>
                                            </div>
                                        </a-card>
                                    </a-col>

                                    <!-- 交易参数面板 -->
                                    <a-col :xs="24" :lg="12">
                                        <a-card title="交易参数" class="params-card">
                                            <a-form layout="vertical">
                                                <a-row :gutter="16">
                                                    <a-col :span="12">
                                                        <a-form-item label="最大杠杆">
                                                            <a-input-number
                                                                v-model:value="tradingParams.maxLeverage"
                                                                :min="1"
                                                                :max="100"
                                                                style="width: 100%"
                                                                @change="updateTradingParams"
                                                            />
                                                        </a-form-item>
                                                    </a-col>
                                                    <a-col :span="12">
                                                        <a-form-item label="最大仓位比例">
                                                            <a-input-number
                                                                v-model:value="tradingParams.maxPositionRatio"
                                                                :min="0.01"
                                                                :max="1"
                                                                :step="0.01"
                                                                style="width: 100%"
                                                                @change="updateTradingParams"
                                                            />
                                                        </a-form-item>
                                                    </a-col>
                                                    <a-col :span="12">
                                                        <a-form-item label="止损比例">
                                                            <a-input-number
                                                                v-model:value="tradingParams.stopLossRatio"
                                                                :min="0.01"
                                                                :max="0.5"
                                                                :step="0.01"
                                                                style="width: 100%"
                                                                @change="updateTradingParams"
                                                            />
                                                        </a-form-item>
                                                    </a-col>
                                                    <a-col :span="12">
                                                        <a-form-item label="止盈比例">
                                                            <a-input-number
                                                                v-model:value="tradingParams.takeProfitRatio"
                                                                :min="0.01"
                                                                :max="1"
                                                                :step="0.01"
                                                                style="width: 100%"
                                                                @change="updateTradingParams"
                                                            />
                                                        </a-form-item>
                                                    </a-col>
                                                    <a-col :span="24">
                                                        <a-form-item label="AI置信度阈值">
                                                            <a-slider
                                                                v-model:value="tradingParams.confidenceThreshold"
                                                                :min="50"
                                                                :max="95"
                                                                :marks="{ 50: '50%', 70: '70%', 90: '90%' }"
                                                                @change="updateTradingParams"
                                                            />
                                                        </a-form-item>
                                                    </a-col>
                                                </a-row>
                                                <a-form-item>
                                                    <a-button
                                                        type="primary"
                                                        :icon="h(SaveOutlined)"
                                                        :loading="saving"
                                                        @click="saveTradingParams"
                                                    >
                                                        保存参数
                                                    </a-button>
                                                </a-form-item>
                                            </a-form>
                                        </a-card>
                                    </a-col>
                                </a-row>
                            </div>

            <!-- 持仓管理页面 -->
            <div id="positions" class="tab-content">
                <div class="page-header">
                    <h1>持仓管理</h1>
                    <p>查看和管理当前持仓</p>
                </div>

                <div class="positions-table">
                    <table id="positions-table">
                        <thead>
                            <tr>
                                <th>交易对</th>
                                <th>方向</th>
                                <th>数量</th>
                                <th>开仓价</th>
                                <th>当前价</th>
                                <th>盈亏</th>
                                <th>杠杆</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="9" class="no-data">暂无持仓数据</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 系统配置页面 -->
            <div id="config" class="tab-content">
                <div class="page-header">
                    <h1>系统配置</h1>
                    <p>配置交易参数和系统设置</p>
                </div>

                <div class="config-sections">
                    <div class="config-section">
                        <h3>交易参数</h3>
                        <div class="config-form">
                            <div class="form-group">
                                <label for="max-leverage">最大杠杆</label>
                                <input type="number" id="max-leverage" min="1" max="100" value="10">
                            </div>
                            <div class="form-group">
                                <label for="max-position">最大仓位比例</label>
                                <input type="number" id="max-position" min="0.01" max="1" step="0.01" value="0.1">
                            </div>
                            <div class="form-group">
                                <label for="stop-loss">止损比例</label>
                                <input type="number" id="stop-loss" min="0.01" max="0.5" step="0.01" value="0.05">
                            </div>
                            <div class="form-group">
                                <label for="take-profit">止盈比例</label>
                                <input type="number" id="take-profit" min="0.01" max="1" step="0.01" value="0.1">
                            </div>
                        </div>
                    </div>

                    <div class="config-section">
                        <h3>风险控制</h3>
                        <div class="config-form">
                            <div class="form-group">
                                <label for="confidence-threshold">置信度阈值</label>
                                <input type="number" id="confidence-threshold" min="50" max="95" value="70">
                            </div>
                            <div class="form-group">
                                <label for="max-daily-loss">最大日损失</label>
                                <input type="number" id="max-daily-loss" min="0.01" max="0.5" step="0.01" value="0.1">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="config-actions">
                    <button id="save-config-btn" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        保存配置
                    </button>
                    <button id="reset-config-btn" class="btn btn-secondary">
                        <i class="fas fa-undo"></i>
                        重置配置
                    </button>
                </div>
            </div>

            <!-- 日志记录页面 -->
            <div id="logs" class="tab-content">
                <div class="page-header">
                    <h1>日志记录</h1>
                    <p>查看系统运行日志和任务状态</p>
                </div>

                <div class="logs-section">
                    <div class="logs-controls">
                        <select id="log-level">
                            <option value="all">所有级别</option>
                            <option value="info">信息</option>
                            <option value="warning">警告</option>
                            <option value="error">错误</option>
                        </select>
                        <button id="clear-logs-btn" class="btn btn-secondary">
                            <i class="fas fa-trash"></i>
                            清空日志
                        </button>
                    </div>
                    <div class="logs-container">
                        <div id="logs-content">
                            <p>正在加载日志...</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

                        </div>
                    </a-layout-content>
                </a-layout>
            </a-layout>
        </a-config-provider>
    </div>

    <!-- Vue应用JavaScript -->
    <script>
        // 等待所有库加载完成
        setTimeout(function() {
            // 配置Day.js
            try {
                if (typeof dayjs !== 'undefined') {
                    if (typeof dayjs_plugin_relativeTime !== 'undefined') {
                        dayjs.extend(dayjs_plugin_relativeTime);
                    }
                    dayjs.locale('zh-cn');
                }
            } catch (e) {
                console.warn('Day.js配置失败:', e);
            }

            const { createApp, ref, reactive, computed, onMounted, h } = Vue;

            // 检查antd是否加载
            if (typeof antd === 'undefined') {
                console.error('Ant Design Vue 未正确加载');
                return;
            }

            const {
                Layout, LayoutHeader, LayoutSider, LayoutContent,
                Menu, MenuItem, Button, Card, Row, Col, Statistic,
                Badge, Space, Avatar, Switch, Dropdown, Tooltip,
                Alert, Checkbox, CheckboxGroup, Form, FormItem,
                InputNumber, Slider, Select, SelectOption, List,
                ListItem, ListItemMeta, Descriptions, DescriptionsItem,
                Divider, Tag, Progress, Empty, ConfigProvider,
                message
            } = antd;

            // 中文语言包
            const zhCN = antd.locales?.zh_CN || {};

            const app = createApp({
            setup() {
                // 响应式数据
                const siderCollapsed = ref(false);
                const currentTab = ref('dashboard');
                const selectedMenuKeys = ref(['dashboard']);
                const isLiveMode = ref(false);
                const envSwitching = ref(false);
                const isTrading = ref(false);
                const tradingToggling = ref(false);
                const refreshing = ref(false);
                const saving = ref(false);
                const showSettings = ref(false);

                // 连接状态
                const connectionStatus = ref('processing');
                const connectionText = computed(() => {
                    switch (connectionStatus.value) {
                        case 'success': return '已连接';
                        case 'error': return '连接失败';
                        case 'processing': return '连接中';
                        default: return '未知状态';
                    }
                });

                // 系统状态
                const systemStatus = reactive({
                    api: 'success',
                    ai: 'success',
                    trading: 'processing'
                });

                // 加载状态
                const loading = reactive({
                    balance: false,
                    pnl: false,
                    positions: false
                });

                // 账户数据
                const accountData = reactive({
                    totalBalance: 130839.39,
                    balanceChange: 2.34,
                    availableBalance: 115628.49,
                    usedMargin: 15210.90,
                    marginRatio: 11.63
                });

                // 交易数据
                const tradingData = reactive({
                    todayPnl: 1234.56,
                    todayPnlPercent: 0.94
                });

                // 持仓数据
                const positionData = reactive({
                    count: 2,
                    longCount: 1,
                    shortCount: 1,
                    totalValue: 25000.00,
                    unrealizedPnl: 456.78,
                    marginUsed: 15210.90,
                    positions: [
                        {
                            symbol: 'BTC/USDT',
                            side: 'long',
                            size: 0.5,
                            entryPrice: 45000,
                            pnl: 234.56
                        },
                        {
                            symbol: 'ETH/USDT',
                            side: 'short',
                            size: 2.0,
                            entryPrice: 3200,
                            pnl: 222.22
                        }
                    ]
                });

                // AI数据
                const aiData = reactive({
                    status: 'success',
                    statusText: '运行中',
                    confidence: 78,
                    lastAnalysis: '2分钟前',
                    nextAnalysis: '3分钟后'
                });

                // 市场数据
                const selectedSymbol = ref('BTC/USDT:USDT');
                const marketData = reactive({
                    price: 45234.56,
                    change24h: 2.34,
                    high24h: 46000,
                    low24h: 44000,
                    volume24h: '1.2B',
                    openInterest: '890M'
                });

                // 交易参数
                const tradingParams = reactive({
                    maxLeverage: 10,
                    maxPositionRatio: 0.1,
                    stopLossRatio: 0.05,
                    takeProfitRatio: 0.1,
                    confidenceThreshold: 70
                });

                // 交易对配置
                const selectedPairs = ref(['BTC/USDT:USDT', 'ETH/USDT:USDT']);
                const availablePairs = [
                    { label: 'BTC/USDT', value: 'BTC/USDT:USDT' },
                    { label: 'ETH/USDT', value: 'ETH/USDT:USDT' },
                    { label: 'BNB/USDT', value: 'BNB/USDT:USDT' },
                    { label: 'ADA/USDT', value: 'ADA/USDT:USDT' },
                    { label: 'SOL/USDT', value: 'SOL/USDT:USDT' },
                    { label: 'DOT/USDT', value: 'DOT/USDT:USDT' }
                ];

                // 方法
                const handleMenuClick = ({ key }) => {
                    currentTab.value = key;
                    selectedMenuKeys.value = [key];
                };

                const handleEnvChange = async (checked) => {
                    envSwitching.value = true;
                    try {
                        // 调用后端API切换环境
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        message.success(checked ? '已切换到实盘模式' : '已切换到模拟盘模式');
                    } catch (error) {
                        message.error('环境切换失败');
                        isLiveMode.value = !checked;
                    } finally {
                        envSwitching.value = false;
                    }
                };

                const refreshData = async () => {
                    refreshing.value = true;
                    try {
                        // 调用后端API刷新数据
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        message.success('数据刷新成功');
                    } catch (error) {
                        message.error('数据刷新失败');
                    } finally {
                        refreshing.value = false;
                    }
                };

                const toggleTrading = async () => {
                    tradingToggling.value = true;
                    try {
                        if (isTrading.value) {
                            await stopTrading();
                        } else {
                            await startTrading();
                        }
                    } finally {
                        tradingToggling.value = false;
                    }
                };

                const startTrading = async () => {
                    try {
                        // 调用后端API启动交易
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        isTrading.value = true;
                        message.success('交易系统已启动');
                    } catch (error) {
                        message.error('启动交易失败');
                    }
                };

                const stopTrading = async () => {
                    try {
                        // 调用后端API停止交易
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        isTrading.value = false;
                        message.success('交易系统已停止');
                    } catch (error) {
                        message.error('停止交易失败');
                    }
                };

                const handleSymbolChange = (value) => {
                    selectedSymbol.value = value;
                    // 刷新市场数据
                };

                const handlePairsChange = (checkedValues) => {
                    selectedPairs.value = checkedValues;
                };

                const updateTradingParams = () => {
                    // 实时更新交易参数
                };

                const saveTradingParams = async () => {
                    saving.value = true;
                    try {
                        // 调用后端API保存参数
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        message.success('参数保存成功');
                    } catch (error) {
                        message.error('参数保存失败');
                    } finally {
                        saving.value = false;
                    }
                };

                const refreshPositions = async () => {
                    loading.positions = true;
                    try {
                        // 调用后端API刷新持仓
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    } catch (error) {
                        message.error('刷新持仓失败');
                    } finally {
                        loading.positions = false;
                    }
                };

                // 生命周期
                onMounted(() => {
                    // 初始化数据
                    refreshData();

                    // 设置定时刷新
                    setInterval(() => {
                        if (!refreshing.value) {
                            // 静默刷新数据
                        }
                    }, 5000);
                });

                return {
                    // 组件引用
                    h,
                    zhCN,
                    // 状态
                    siderCollapsed, currentTab, selectedMenuKeys,
                    isLiveMode, envSwitching, isTrading, tradingToggling,
                    refreshing, saving, showSettings,
                    connectionStatus, connectionText, systemStatus, loading,
                    // 数据
                    accountData, tradingData, positionData, aiData,
                    selectedSymbol, marketData, tradingParams,
                    selectedPairs, availablePairs,
                    // 方法
                    handleMenuClick, handleEnvChange, refreshData,
                    toggleTrading, startTrading, stopTrading,
                    handleSymbolChange, handlePairsChange,
                    updateTradingParams, saveTradingParams, refreshPositions
                };
            }
        });

            // 注册Ant Design组件
            app.use(antd);

            // 挂载应用
            app.mount('#app');
        }, 1000);
    </script>

    <!-- 自定义脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/charts.js"></script>
</body>
</html>
