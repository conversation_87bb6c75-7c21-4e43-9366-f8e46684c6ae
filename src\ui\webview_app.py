# -*- coding: utf-8 -*-
"""
PyWebview桌面应用
使用PyWebview创建跨平台的桌面应用，提供交易系统的用户界面

Author: SuperBot Team
Date: 2025-01-05
"""

import os
import sys
import threading
import time
from pathlib import Path
from typing import Dict, Any, Optional

import webview
from webview.window import Window

from src.ui.api import WebviewAPI
from src.utils.logger import get_logger
from src.utils.config import get_config_manager

logger = get_logger(__name__)


class SuperBotWebviewApp:
    """SuperBot PyWebview桌面应用"""
    
    def __init__(self):
        """初始化应用"""
        self.window: Optional[Window] = None
        self.api = WebviewAPI()
        self.config_manager = get_config_manager()
        
        # 应用配置
        self.app_config = {
            'title': 'SuperBot - 加密货币量化交易系统',
            'width': 1200,
            'height': 800,
            'min_width': 800,
            'min_height': 600,
            'resizable': True,
            'fullscreen': False,
            'minimized': False,
            'on_top': False,
            'shadow': True,
            'debug': False
        }
        
        # 窗口状态
        self.window_state = {
            'is_running': False,
            'is_minimized': False,
            'is_maximized': False,
            'position': {'x': 100, 'y': 100},
            'size': {'width': 1200, 'height': 800}
        }
        
        # 加载配置
        self._load_app_config()
        
        logger.info("PyWebview应用初始化完成")
    
    def _load_app_config(self):
        """加载应用配置"""
        try:
            # 从配置管理器加载窗口配置
            window_config = self.config_manager.get('ui.window', {})

            if window_config and isinstance(window_config, dict):
                self.app_config.update(window_config)
                logger.info("应用配置加载成功")
            else:
                logger.info("使用默认应用配置")

        except Exception as e:
            logger.error(f"加载应用配置失败: {e}")
            logger.info("使用默认应用配置")
    
    def _save_app_config(self):
        """保存应用配置"""
        try:
            # 更新窗口状态到配置
            if self.window:
                # 获取当前窗口状态
                self.window_state['size']['width'] = self.app_config['width']
                self.window_state['size']['height'] = self.app_config['height']
            
            # 保存到配置管理器
            self.config_manager.set('ui.window', self.app_config)
            self.config_manager.set('ui.window_state', self.window_state)
            
            logger.info("应用配置保存成功")
            
        except Exception as e:
            logger.error(f"保存应用配置失败: {e}")
    
    def _get_static_path(self) -> str:
        """获取静态资源路径"""
        try:
            # 获取当前文件的目录
            current_dir = Path(__file__).parent
            static_dir = current_dir / 'static'
            
            # 确保静态资源目录存在
            if not static_dir.exists():
                static_dir.mkdir(parents=True, exist_ok=True)
                logger.warning(f"静态资源目录不存在，已创建: {static_dir}")
            
            # 检查index.html是否存在
            index_file = static_dir / 'index.html'
            if not index_file.exists():
                # 创建基本的index.html
                self._create_basic_index_html(index_file)
            
            return str(static_dir)
            
        except Exception as e:
            logger.error(f"获取静态资源路径失败: {e}")
            return str(Path(__file__).parent / 'static')
    
    def _create_basic_index_html(self, index_file: Path):
        """创建基本的index.html文件"""
        try:
            basic_html = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SuperBot - 加密货币量化交易系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #667eea;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .loading {
            text-align: center;
            padding: 50px;
        }
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .status {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .status h3 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SuperBot</h1>
            <p>加密货币量化交易系统</p>
        </div>
        
        <div class="loading">
            <div class="loading-spinner"></div>
            <p>系统正在初始化...</p>
        </div>
        
        <div class="status">
            <h3>系统状态</h3>
            <p id="system-status">正在检查系统状态...</p>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button class="btn" onclick="checkSystemStatus()">检查系统状态</button>
            <button class="btn" onclick="startTrading()" id="start-btn" disabled>开始交易</button>
            <button class="btn" onclick="stopTrading()" id="stop-btn" disabled>停止交易</button>
        </div>
    </div>

    <script>
        // 检查系统状态
        async function checkSystemStatus() {
            try {
                const status = await pywebview.api.get_system_status();
                document.getElementById('system-status').textContent = 
                    `系统状态: ${status.status || '未知'} | 版本: ${status.version || '未知'}`;
                
                // 更新按钮状态
                const startBtn = document.getElementById('start-btn');
                const stopBtn = document.getElementById('stop-btn');
                
                if (status.trading_active) {
                    startBtn.disabled = true;
                    stopBtn.disabled = false;
                } else {
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                }
                
            } catch (error) {
                console.error('检查系统状态失败:', error);
                document.getElementById('system-status').textContent = '检查系统状态失败';
            }
        }
        
        // 开始交易
        async function startTrading() {
            try {
                const result = await pywebview.api.start_trading();
                if (result.success) {
                    alert('交易已开始');
                    checkSystemStatus();
                } else {
                    alert('开始交易失败: ' + result.message);
                }
            } catch (error) {
                console.error('开始交易失败:', error);
                alert('开始交易失败');
            }
        }
        
        // 停止交易
        async function stopTrading() {
            try {
                const result = await pywebview.api.stop_trading();
                if (result.success) {
                    alert('交易已停止');
                    checkSystemStatus();
                } else {
                    alert('停止交易失败: ' + result.message);
                }
            } catch (error) {
                console.error('停止交易失败:', error);
                alert('停止交易失败');
            }
        }
        
        // 页面加载完成后检查系统状态
        window.addEventListener('load', function() {
            setTimeout(checkSystemStatus, 1000);
        });
        
        // 定期更新系统状态
        setInterval(checkSystemStatus, 30000); // 每30秒更新一次
    </script>
</body>
</html>'''
            
            with open(index_file, 'w', encoding='utf-8') as f:
                f.write(basic_html)
            
            logger.info(f"创建基本index.html文件: {index_file}")
            
        except Exception as e:
            logger.error(f"创建基本index.html文件失败: {e}")
    
    def _on_window_loaded(self):
        """窗口加载完成回调"""
        try:
            logger.info("应用窗口加载完成")
            self.window_state['is_running'] = True
            
            # 初始化API
            self.api.initialize()
            
        except Exception as e:
            logger.error(f"窗口加载完成回调失败: {e}")
    
    def _on_window_closing(self):
        """窗口关闭回调"""
        try:
            logger.info("应用窗口正在关闭")
            
            # 保存配置
            self._save_app_config()
            
            # 清理资源
            self.api.cleanup()
            
            self.window_state['is_running'] = False
            
        except Exception as e:
            logger.error(f"窗口关闭回调失败: {e}")
    
    def create_window(self) -> Window:
        """创建应用窗口"""
        try:
            # 获取静态资源路径
            static_path = self._get_static_path()
            
            # 创建窗口
            self.window = webview.create_window(
                title=self.app_config['title'],
                url=f"file://{static_path}/index.html",
                js_api=self.api,
                width=self.app_config['width'],
                height=self.app_config['height'],
                min_size=(self.app_config['min_width'], self.app_config['min_height']),
                resizable=self.app_config['resizable'],
                fullscreen=self.app_config['fullscreen'],
                minimized=self.app_config['minimized'],
                on_top=self.app_config['on_top'],
                shadow=self.app_config['shadow']
            )
            
            logger.info(f"应用窗口创建成功: {self.app_config['title']}")
            return self.window
            
        except Exception as e:
            logger.error(f"创建应用窗口失败: {e}")
            raise
    
    def start(self, debug: bool = False):
        """启动应用"""
        try:
            logger.info("启动PyWebview应用")
            
            # 设置调试模式
            self.app_config['debug'] = debug
            
            # 创建窗口
            self.create_window()
            
            # 启动webview
            webview.start(
                debug=debug,
                http_server=False,
                private_mode=False
            )
            
        except Exception as e:
            logger.error(f"启动应用失败: {e}")
            raise
        finally:
            # 清理资源
            self._on_window_closing()
    
    def stop(self):
        """停止应用"""
        try:
            logger.info("停止PyWebview应用")
            
            if self.window:
                self.window.destroy()
            
            self.window_state['is_running'] = False
            
        except Exception as e:
            logger.error(f"停止应用失败: {e}")
    
    def restart(self):
        """重启应用"""
        try:
            logger.info("重启PyWebview应用")
            
            # 停止当前应用
            self.stop()
            
            # 等待一段时间
            time.sleep(1)
            
            # 重新启动
            self.start()
            
        except Exception as e:
            logger.error(f"重启应用失败: {e}")
    
    def get_window_state(self) -> Dict[str, Any]:
        """获取窗口状态"""
        return self.window_state.copy()
    
    def set_window_config(self, config: Dict[str, Any]):
        """设置窗口配置"""
        try:
            self.app_config.update(config)
            self._save_app_config()
            logger.info("窗口配置更新成功")
            
        except Exception as e:
            logger.error(f"设置窗口配置失败: {e}")


def main():
    """主函数"""
    try:
        # 创建应用实例
        app = SuperBotWebviewApp()
        
        # 启动应用
        app.start(debug=True)
        
    except KeyboardInterrupt:
        logger.info("用户中断应用")
    except Exception as e:
        logger.error(f"应用运行异常: {e}")
    finally:
        logger.info("应用已退出")


if __name__ == "__main__":
    main()
