{"timestamp":"2025-08-05T01:54:20.837581","level":"ERROR","logger":"__main__","module":"test_logging_system","function":"test_basic_logging","line":30,"message":"这是一条ERROR日志","thread_id":6412,"thread_name":"MainThread","process_id":16812}
{"timestamp":"2025-08-05T01:54:20.838580","level":"CRITICAL","logger":"__main__","module":"test_logging_system","function":"test_basic_logging","line":31,"message":"这是一条CRITICAL日志","thread_id":6412,"thread_name":"MainThread","process_id":16812}
{"timestamp":"2025-08-05T01:54:20.840581","level":"ERROR","logger":"test_module","module":"logger","function":"log_error","line":325,"message":"错误: ZeroDivisionError - division by zero","thread_id":6412,"thread_name":"MainThread","process_id":16812,"exception":"Traceback (most recent call last):\n  File \"D:\\job\\Superbot\\test_logging_system.py\", line 77, in test_error_logging\n    result = 1 / 0\n             ~~^~~\nZeroDivisionError: division by zero","extra":{"error_type":"ZeroDivisionError","error_message":"division by zero","module":"test_module","timestamp":"2025-08-05T01:54:20.840580","context":{"operation":"division","dividend":1,"divisor":0,"user_id":"test_user"}}}
{"timestamp":"2025-08-05T01:54:20.993355","level":"ERROR","logger":"__main__","module":"test_logging_system","function":"test_database_logging","line":227,"message":"这是一条ERROR日志，会写入数据库","thread_id":6412,"thread_name":"MainThread","process_id":16812}
{"timestamp":"2025-08-05T01:54:20.994377","level":"CRITICAL","logger":"__main__","module":"test_logging_system","function":"test_database_logging","line":228,"message":"这是一条CRITICAL日志，会写入数据库","thread_id":6412,"thread_name":"MainThread","process_id":16812}
{"timestamp":"2025-08-05T02:14:32.383824","level":"ERROR","logger":"src.services.ai_service","module":"ai_service","function":"_get_api_key","line":64,"message":"获取DeepSeek API密钥失败: 未找到有效的DeepSeek API密钥","thread_id":2880,"thread_name":"MainThread","process_id":8720}
{"timestamp":"2025-08-05T02:14:32.384823","level":"ERROR","logger":"src.services.ai_service","module":"ai_service","function":"_get_api_key","line":64,"message":"获取DeepSeek API密钥失败: 未找到有效的DeepSeek API密钥","thread_id":2880,"thread_name":"MainThread","process_id":8720}
{"timestamp":"2025-08-05T02:14:32.385815","level":"ERROR","logger":"src.services.ai_service","module":"ai_service","function":"_make_api_request","line":133,"message":"AI服务调用异常: 获取API密钥失败: 未找到有效的DeepSeek API密钥","thread_id":2880,"thread_name":"MainThread","process_id":8720}
{"timestamp":"2025-08-05T02:14:32.386815","level":"ERROR","logger":"src.services.ai_service","module":"ai_service","function":"test_connection","line":320,"message":"AI服务连接测试失败: AI服务调用异常: 获取API密钥失败: 未找到有效的DeepSeek API密钥","thread_id":2880,"thread_name":"MainThread","process_id":8720}
{"timestamp":"2025-08-05T02:14:32.392819","level":"ERROR","logger":"src.services.notification_service","module":"notification_service","function":"_send_to_log","line":228,"message":"通知: 系统错误 - 测试错误通知","thread_id":2880,"thread_name":"MainThread","process_id":8720}
{"timestamp":"2025-08-05T02:14:52.073625","level":"ERROR","logger":"src.services.exchange_service","module":"exchange_service","function":"_get_exchange_instance","line":92,"message":"交易所认证失败: okx, 错误: okx {\"msg\":\"APIKey does not match current environment.\",\"code\":\"50101\"}","thread_id":7848,"thread_name":"MainThread","process_id":18808}
{"timestamp":"2025-08-05T02:14:52.074614","level":"ERROR","logger":"src.services.exchange_service","module":"exchange_service","function":"test_connection","line":147,"message":"交易所 okx 连接测试失败: 交易所认证失败: okx {\"msg\":\"APIKey does not match current environment.\",\"code\":\"50101\"}","thread_id":7848,"thread_name":"MainThread","process_id":18808}
{"timestamp":"2025-08-05T02:15:08.695084","level":"ERROR","logger":"src.services.exchange_service","module":"exchange_service","function":"_get_exchange_instance","line":92,"message":"交易所认证失败: okx, 错误: okx {\"msg\":\"APIKey does not match current environment.\",\"code\":\"50101\"}","thread_id":7848,"thread_name":"MainThread","process_id":18808}
{"timestamp":"2025-08-05T02:15:08.696084","level":"ERROR","logger":"src.services.exchange_service","module":"exchange_service","function":"test_connection","line":147,"message":"交易所 okx 连接测试失败: 交易所认证失败: okx {\"msg\":\"APIKey does not match current environment.\",\"code\":\"50101\"}","thread_id":7848,"thread_name":"MainThread","process_id":18808}
{"timestamp":"2025-08-05T02:15:08.703080","level":"ERROR","logger":"src.services.notification_service","module":"notification_service","function":"_send_to_log","line":228,"message":"通知: 系统错误 - 测试错误通知","thread_id":7848,"thread_name":"MainThread","process_id":18808}
{"timestamp":"2025-08-05T02:15:08.960388","level":"ERROR","logger":"src.services.exchange_service","module":"exchange_service","function":"_get_exchange_instance","line":92,"message":"交易所认证失败: okx, 错误: okx {\"msg\":\"APIKey does not match current environment.\",\"code\":\"50101\"}","thread_id":7848,"thread_name":"MainThread","process_id":18808}
{"timestamp":"2025-08-05T02:15:08.961366","level":"ERROR","logger":"src.services.exchange_service","module":"exchange_service","function":"test_connection","line":147,"message":"交易所 okx 连接测试失败: 交易所认证失败: okx {\"msg\":\"APIKey does not match current environment.\",\"code\":\"50101\"}","thread_id":7848,"thread_name":"MainThread","process_id":18808}
{"timestamp":"2025-08-05T02:16:34.432068","level":"ERROR","logger":"src.services.exchange_service","module":"exchange_service","function":"_get_exchange_instance","line":92,"message":"交易所认证失败: okx, 错误: okx {\"msg\":\"APIKey does not match current environment.\",\"code\":\"50101\"}","thread_id":20728,"thread_name":"MainThread","process_id":22988}
{"timestamp":"2025-08-05T02:16:34.434064","level":"ERROR","logger":"src.services.exchange_service","module":"exchange_service","function":"test_connection","line":147,"message":"交易所 okx 连接测试失败: 交易所认证失败: okx {\"msg\":\"APIKey does not match current environment.\",\"code\":\"50101\"}","thread_id":20728,"thread_name":"MainThread","process_id":22988}
{"timestamp":"2025-08-05T02:16:48.767002","level":"ERROR","logger":"src.services.exchange_service","module":"exchange_service","function":"_get_exchange_instance","line":92,"message":"交易所认证失败: okx, 错误: okx {\"msg\":\"APIKey does not match current environment.\",\"code\":\"50101\"}","thread_id":20728,"thread_name":"MainThread","process_id":22988}
{"timestamp":"2025-08-05T02:16:48.768977","level":"ERROR","logger":"src.services.exchange_service","module":"exchange_service","function":"test_connection","line":147,"message":"交易所 okx 连接测试失败: 交易所认证失败: okx {\"msg\":\"APIKey does not match current environment.\",\"code\":\"50101\"}","thread_id":20728,"thread_name":"MainThread","process_id":22988}
{"timestamp":"2025-08-05T02:16:48.775987","level":"ERROR","logger":"src.services.notification_service","module":"notification_service","function":"_send_to_log","line":228,"message":"通知: 系统错误 - 测试错误通知","thread_id":20728,"thread_name":"MainThread","process_id":22988}
{"timestamp":"2025-08-05T02:16:49.268741","level":"ERROR","logger":"src.services.exchange_service","module":"exchange_service","function":"_get_exchange_instance","line":92,"message":"交易所认证失败: okx, 错误: okx {\"msg\":\"APIKey does not match current environment.\",\"code\":\"50101\"}","thread_id":20728,"thread_name":"MainThread","process_id":22988}
{"timestamp":"2025-08-05T02:16:49.269733","level":"ERROR","logger":"src.services.exchange_service","module":"exchange_service","function":"test_connection","line":147,"message":"交易所 okx 连接测试失败: 交易所认证失败: okx {\"msg\":\"APIKey does not match current environment.\",\"code\":\"50101\"}","thread_id":20728,"thread_name":"MainThread","process_id":22988}
{"timestamp":"2025-08-05T02:18:08.243930","level":"ERROR","logger":"src.services.notification_service","module":"notification_service","function":"_send_to_log","line":228,"message":"通知: 系统错误 - 测试错误通知","thread_id":13852,"thread_name":"MainThread","process_id":14452}
{"timestamp":"2025-08-05T02:26:10.787944","level":"ERROR","logger":"src.services.exchange_service","module":"exchange_service","function":"_handle_exchange_error","line":151,"message":"交易所操作失败: 创建市价单: okx BTC/USDT:USDT buy 0.001, 错误: okx amount of BTC/USDT:USDT must be greater than minimum amount precision of 0.01","thread_id":12200,"thread_name":"MainThread","process_id":23288}
{"timestamp":"2025-08-05T02:26:25.980797","level":"ERROR","logger":"src.services.exchange_service","module":"exchange_service","function":"_handle_exchange_error","line":151,"message":"交易所操作失败: 获取 okx INVALID/PAIR:USDT 行情, 错误: okx does not have market symbol INVALID/PAIR:USDT","thread_id":12200,"thread_name":"MainThread","process_id":23288}
{"timestamp":"2025-08-05T02:35:50.809580","level":"ERROR","logger":"src.services.exchange_service","module":"exchange_service","function":"_handle_exchange_error","line":151,"message":"交易所操作失败: 获取 okx INVALID/PAIR:USDT K线数据, 错误: okx does not have market symbol INVALID/PAIR:USDT","thread_id":20496,"thread_name":"MainThread","process_id":4444}
{"timestamp":"2025-08-05T02:35:50.810612","level":"ERROR","logger":"src.services.market_data_service","module":"market_data_service","function":"get_ohlcv_data","line":210,"message":"获取OHLCV数据失败: INVALID/PAIR:USDT 1h, 错误: 交易所操作失败: okx does not have market symbol INVALID/PAIR:USDT","thread_id":20496,"thread_name":"MainThread","process_id":4444}
{"timestamp":"2025-08-05T02:35:50.811598","level":"ERROR","logger":"src.services.market_data_service","module":"market_data_service","function":"calculate_all_indicators","line":443,"message":"计算技术指标失败: INVALID/PAIR:USDT 1h, 错误: 获取OHLCV数据失败: 交易所操作失败: okx does not have market symbol INVALID/PAIR:USDT","thread_id":20496,"thread_name":"MainThread","process_id":4444}
{"timestamp":"2025-08-05T02:35:51.811239","level":"ERROR","logger":"src.services.exchange_service","module":"exchange_service","function":"_handle_exchange_error","line":151,"message":"交易所操作失败: 获取 okx BTC/USDT:USDT K线数据, 错误: invalid literal for int() with base 10: 'invali'","thread_id":20496,"thread_name":"MainThread","process_id":4444}
{"timestamp":"2025-08-05T02:35:51.812254","level":"ERROR","logger":"src.services.market_data_service","module":"market_data_service","function":"get_ohlcv_data","line":210,"message":"获取OHLCV数据失败: BTC/USDT:USDT invalid, 错误: 交易所操作失败: invalid literal for int() with base 10: 'invali'","thread_id":20496,"thread_name":"MainThread","process_id":4444}
{"timestamp":"2025-08-05T02:35:51.813254","level":"ERROR","logger":"src.services.market_data_service","module":"market_data_service","function":"calculate_all_indicators","line":443,"message":"计算技术指标失败: BTC/USDT:USDT invalid, 错误: 获取OHLCV数据失败: 交易所操作失败: invalid literal for int() with base 10: 'invali'","thread_id":20496,"thread_name":"MainThread","process_id":4444}
{"timestamp":"2025-08-05T02:39:25.979760","level":"ERROR","logger":"src.services.notification_service","module":"notification_service","function":"_send_to_log","line":342,"message":"通知: ERROR级别测试 - 错误通知","thread_id":21440,"thread_name":"Thread-1 (_push_worker)","process_id":21204}
{"timestamp":"2025-08-05T02:39:25.980759","level":"CRITICAL","logger":"src.services.notification_service","module":"notification_service","function":"_send_to_log","line":344,"message":"通知: CRITICAL级别测试 - 严重错误通知","thread_id":21440,"thread_name":"Thread-1 (_push_worker)","process_id":21204}
{"timestamp":"2025-08-05T02:39:25.988761","level":"ERROR","logger":"src.services.notification_service","module":"notification_service","function":"_send_to_log","line":342,"message":"通知: ERROR测试 - 这条ERROR通知应该通过过滤","thread_id":21440,"thread_name":"Thread-1 (_push_worker)","process_id":21204}
{"timestamp":"2025-08-05T03:00:58.691464","level":"ERROR","logger":"src.core.analysis.data_processor","module":"data_processor","function":"process_ohlcv_data","line":504,"message":"数据处理失败: [Errno 22] Invalid argument","thread_id":21668,"thread_name":"MainThread","process_id":21060}
{"timestamp":"2025-08-05T03:00:58.695462","level":"ERROR","logger":"src.core.analysis.data_processor","module":"data_processor","function":"process_ohlcv_data","line":504,"message":"数据处理失败: [Errno 22] Invalid argument","thread_id":21668,"thread_name":"MainThread","process_id":21060}
{"timestamp":"2025-08-05T03:00:58.754132","level":"ERROR","logger":"src.core.analysis.data_processor","module":"data_processor","function":"convert_from_dataframe","line":556,"message":"从DataFrame转换失败: Converting from datetime64[ns] to int32 is not supported. Do obj.astype('int64').astype(dtype) instead","thread_id":21668,"thread_name":"MainThread","process_id":21060}
{"timestamp":"2025-08-05T03:00:58.757122","level":"ERROR","logger":"src.core.analysis.data_processor","module":"data_processor","function":"process_ohlcv_data","line":504,"message":"数据处理失败: [Errno 22] Invalid argument","thread_id":21668,"thread_name":"MainThread","process_id":21060}
{"timestamp":"2025-08-05T03:19:24.328672","level":"ERROR","logger":"src.core.ai_engines.base_engine","module":"base_engine","function":"_validate_response","line":443,"message":"响应验证失败: 置信度超出配置范围: 150","thread_id":7444,"thread_name":"MainThread","process_id":20036}
{"timestamp":"2025-08-05T03:19:24.331663","level":"ERROR","logger":"src.core.ai_engines.base_engine","module":"base_engine","function":"_validate_request","line":193,"message":"请求验证失败: 缺少必需的交易对或交易所信息","thread_id":7444,"thread_name":"MainThread","process_id":20036}
{"timestamp":"2025-08-05T03:19:24.333662","level":"ERROR","logger":"src.core.ai_engines.base_engine","module":"base_engine","function":"update_config","line":622,"message":"更新引擎配置失败: 'ConfigRepository' object has no attribute 'save_ai_engine_config'","thread_id":7444,"thread_name":"MainThread","process_id":20036}
{"timestamp":"2025-08-05T03:20:02.620380","level":"ERROR","logger":"src.core.ai_engines.base_engine","module":"base_engine","function":"_validate_response","line":443,"message":"响应验证失败: 置信度超出配置范围: 150","thread_id":2104,"thread_name":"MainThread","process_id":17724}
{"timestamp":"2025-08-05T03:20:02.622381","level":"ERROR","logger":"src.core.ai_engines.base_engine","module":"base_engine","function":"_validate_request","line":193,"message":"请求验证失败: 缺少必需的交易对或交易所信息","thread_id":2104,"thread_name":"MainThread","process_id":17724}
{"timestamp":"2025-08-05T03:54:37.428952","level":"ERROR","logger":"src.core.ai_engines.prompts","module":"prompts","function":"validate_params","line":35,"message":"缺少必需参数: exchange","thread_id":6564,"thread_name":"MainThread","process_id":12972}
{"timestamp":"2025-08-05T03:54:37.430951","level":"ERROR","logger":"src.core.ai_engines.prompts","module":"prompts","function":"format_prompt","line":451,"message":"格式化提示词失败: nonexistent_template, 错误: 未找到模板: nonexistent_template","thread_id":6564,"thread_name":"MainThread","process_id":12972}
{"timestamp":"2025-08-05T03:55:10.287092","level":"ERROR","logger":"src.core.ai_engines.prompts","module":"prompts","function":"validate_params","line":35,"message":"缺少必需参数: exchange","thread_id":12968,"thread_name":"MainThread","process_id":6412}
{"timestamp":"2025-08-05T03:55:10.290093","level":"ERROR","logger":"src.core.ai_engines.prompts","module":"prompts","function":"format_prompt","line":451,"message":"格式化提示词失败: nonexistent_template, 错误: 未找到模板: nonexistent_template","thread_id":12968,"thread_name":"MainThread","process_id":6412}
{"timestamp":"2025-08-05T03:56:41.736423","level":"ERROR","logger":"src.core.ai_engines.prompts","module":"prompts","function":"validate_params","line":35,"message":"缺少必需参数: exchange","thread_id":3552,"thread_name":"MainThread","process_id":6828}
{"timestamp":"2025-08-05T03:56:41.739418","level":"ERROR","logger":"src.core.ai_engines.prompts","module":"prompts","function":"format_prompt","line":451,"message":"格式化提示词失败: nonexistent_template, 错误: 未找到模板: nonexistent_template","thread_id":3552,"thread_name":"MainThread","process_id":6828}
{"timestamp":"2025-08-05T03:57:20.757306","level":"ERROR","logger":"src.core.ai_engines.prompts","module":"prompts","function":"validate_params","line":35,"message":"缺少必需参数: exchange","thread_id":21396,"thread_name":"MainThread","process_id":18064}
{"timestamp":"2025-08-05T03:57:20.759311","level":"ERROR","logger":"src.core.ai_engines.prompts","module":"prompts","function":"format_prompt","line":451,"message":"格式化提示词失败: nonexistent_template, 错误: 未找到模板: nonexistent_template","thread_id":21396,"thread_name":"MainThread","process_id":18064}
{"timestamp":"2025-08-05T03:57:45.495352","level":"ERROR","logger":"src.core.ai_engines.prompts","module":"prompts","function":"validate_params","line":35,"message":"缺少必需参数: exchange","thread_id":12592,"thread_name":"MainThread","process_id":21028}
{"timestamp":"2025-08-05T04:08:37.520705","level":"ERROR","logger":"src.core.trading.executor","module":"executor","function":"_validate_open_params","line":633,"message":"无效的交易对","thread_id":21480,"thread_name":"MainThread","process_id":6488}
{"timestamp":"2025-08-05T04:08:37.521712","level":"ERROR","logger":"src.core.trading.executor","module":"executor","function":"_validate_open_params","line":638,"message":"无效的持仓方向","thread_id":21480,"thread_name":"MainThread","process_id":6488}
{"timestamp":"2025-08-05T04:08:37.522710","level":"ERROR","logger":"src.core.trading.executor","module":"executor","function":"_validate_open_params","line":646,"message":"订单数量超出范围: 0.0001, 范围: [0.001, 10.0]","thread_id":21480,"thread_name":"MainThread","process_id":6488}
{"timestamp":"2025-08-05T04:08:37.523708","level":"ERROR","logger":"src.core.trading.executor","module":"executor","function":"_validate_open_params","line":651,"message":"杠杆倍数超出范围: 200","thread_id":21480,"thread_name":"MainThread","process_id":6488}
{"timestamp":"2025-08-05T07:49:19.918082","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":23312,"thread_name":"MainThread","process_id":17092}
{"timestamp":"2025-08-05T07:49:19.920079","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":23312,"thread_name":"MainThread","process_id":17092}
{"timestamp":"2025-08-05T07:49:19.922082","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":23312,"thread_name":"MainThread","process_id":17092}
{"timestamp":"2025-08-05T07:49:19.923084","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":23312,"thread_name":"MainThread","process_id":17092}
{"timestamp":"2025-08-05T07:49:19.924083","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":23312,"thread_name":"MainThread","process_id":17092}
{"timestamp":"2025-08-05T07:49:19.925079","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":23312,"thread_name":"MainThread","process_id":17092}
{"timestamp":"2025-08-05T07:49:19.926073","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":23312,"thread_name":"MainThread","process_id":17092}
{"timestamp":"2025-08-05T07:49:19.928082","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":23312,"thread_name":"MainThread","process_id":17092}
{"timestamp":"2025-08-05T07:49:19.929082","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":23312,"thread_name":"MainThread","process_id":17092}
{"timestamp":"2025-08-05T07:49:19.930086","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":23312,"thread_name":"MainThread","process_id":17092}
{"timestamp":"2025-08-05T07:49:19.931086","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":23312,"thread_name":"MainThread","process_id":17092}
{"timestamp":"2025-08-05T07:49:19.932089","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":23312,"thread_name":"MainThread","process_id":17092}
{"timestamp":"2025-08-05T07:49:19.933087","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":23312,"thread_name":"MainThread","process_id":17092}
{"timestamp":"2025-08-05T07:49:19.934086","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":23312,"thread_name":"MainThread","process_id":17092}
{"timestamp":"2025-08-05T07:49:19.936097","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":23312,"thread_name":"MainThread","process_id":17092}
{"timestamp":"2025-08-05T07:49:19.937088","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":23312,"thread_name":"MainThread","process_id":17092}
{"timestamp":"2025-08-05T07:49:19.939093","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":23312,"thread_name":"MainThread","process_id":17092}
{"timestamp":"2025-08-05T07:49:19.940094","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":23312,"thread_name":"MainThread","process_id":17092}
{"timestamp":"2025-08-05T07:49:19.943100","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":23312,"thread_name":"MainThread","process_id":17092}
{"timestamp":"2025-08-05T07:49:49.353772","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":12784,"thread_name":"MainThread","process_id":5956}
{"timestamp":"2025-08-05T07:49:49.357771","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":12784,"thread_name":"MainThread","process_id":5956}
{"timestamp":"2025-08-05T07:49:49.358771","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":12784,"thread_name":"MainThread","process_id":5956}
{"timestamp":"2025-08-05T07:49:49.361770","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":12784,"thread_name":"MainThread","process_id":5956}
{"timestamp":"2025-08-05T07:49:49.363768","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":12784,"thread_name":"MainThread","process_id":5956}
{"timestamp":"2025-08-05T07:49:49.364769","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":12784,"thread_name":"MainThread","process_id":5956}
{"timestamp":"2025-08-05T08:02:33.821587","level":"ERROR","logger":"src.core.trading.position_manager","module":"position_manager","function":"get_position","line":189,"message":"获取仓位信息异常: ExchangeService.get_positions() missing 1 required positional argument: 'exchange_name'","thread_id":21568,"thread_name":"MainThread","process_id":12244}
{"timestamp":"2025-08-05T08:02:33.823594","level":"ERROR","logger":"src.core.trading.position_manager","module":"position_manager","function":"get_all_positions","line":236,"message":"获取所有仓位异常: ExchangeService.get_positions() missing 1 required positional argument: 'exchange_name'","thread_id":21568,"thread_name":"MainThread","process_id":12244}
{"timestamp":"2025-08-05T08:02:33.828582","level":"ERROR","logger":"src.core.trading.position_manager","module":"position_manager","function":"get_all_positions","line":236,"message":"获取所有仓位异常: ExchangeService.get_positions() missing 1 required positional argument: 'exchange_name'","thread_id":21568,"thread_name":"MainThread","process_id":12244}
{"timestamp":"2025-08-05T08:02:33.838601","level":"ERROR","logger":"src.core.trading.position_manager","module":"position_manager","function":"split_position","line":532,"message":"分割比例必须在0和1之间","thread_id":21568,"thread_name":"MainThread","process_id":12244}
{"timestamp":"2025-08-05T08:03:01.427914","level":"ERROR","logger":"src.core.trading.position_manager","module":"position_manager","function":"get_all_positions","line":236,"message":"获取所有仓位异常: ExchangeService.get_positions() missing 1 required positional argument: 'exchange_name'","thread_id":2932,"thread_name":"MainThread","process_id":22892}
{"timestamp":"2025-08-05T08:06:04.940691","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":6504,"thread_name":"MainThread","process_id":8768}
{"timestamp":"2025-08-05T08:06:04.942307","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":6504,"thread_name":"MainThread","process_id":8768}
{"timestamp":"2025-08-05T08:06:04.945653","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":6504,"thread_name":"MainThread","process_id":8768}
{"timestamp":"2025-08-05T08:06:04.949182","level":"ERROR","logger":"src.core.trading.risk_manager","module":"risk_manager","function":"_check_emergency_stop_conditions","line":820,"message":"检查紧急停止条件异常: ExchangeService.get_account_balance() missing 1 required positional argument: 'exchange_name'","thread_id":6504,"thread_name":"MainThread","process_id":8768}
{"timestamp":"2025-08-05T08:06:04.951182","level":"ERROR","logger":"src.core.trading.position_manager","module":"position_manager","function":"get_all_positions","line":236,"message":"获取所有仓位异常: ExchangeService.get_positions() missing 1 required positional argument: 'exchange_name'","thread_id":6504,"thread_name":"MainThread","process_id":8768}
{"timestamp":"2025-08-05T08:14:05.906959","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":21816,"thread_name":"MainThread","process_id":8736}
{"timestamp":"2025-08-05T08:14:05.909957","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":21816,"thread_name":"MainThread","process_id":8736}
{"timestamp":"2025-08-05T08:21:03.354582","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":19740,"thread_name":"MainThread","process_id":19196}
{"timestamp":"2025-08-05T08:21:03.356582","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":19740,"thread_name":"MainThread","process_id":19196}
{"timestamp":"2025-08-05T08:21:03.358580","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":19740,"thread_name":"MainThread","process_id":19196}
{"timestamp":"2025-08-05T08:21:03.359578","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":19740,"thread_name":"MainThread","process_id":19196}
{"timestamp":"2025-08-05T08:21:03.362581","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":19740,"thread_name":"MainThread","process_id":19196}
{"timestamp":"2025-08-05T08:21:03.363585","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":19740,"thread_name":"MainThread","process_id":19196}
{"timestamp":"2025-08-05T08:21:03.366577","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":19740,"thread_name":"MainThread","process_id":19196}
{"timestamp":"2025-08-05T08:21:03.370605","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":19740,"thread_name":"MainThread","process_id":19196}
{"timestamp":"2025-08-05T08:21:03.373583","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":19740,"thread_name":"MainThread","process_id":19196}
{"timestamp":"2025-08-05T08:24:26.327561","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":6156,"thread_name":"MainThread","process_id":10936}
{"timestamp":"2025-08-05T08:24:26.329566","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":6156,"thread_name":"MainThread","process_id":10936}
{"timestamp":"2025-08-05T08:24:26.331571","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":6156,"thread_name":"MainThread","process_id":10936}
{"timestamp":"2025-08-05T08:24:26.333573","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":6156,"thread_name":"MainThread","process_id":10936}
{"timestamp":"2025-08-05T08:24:26.335569","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":6156,"thread_name":"MainThread","process_id":10936}
{"timestamp":"2025-08-05T08:24:26.337584","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":6156,"thread_name":"MainThread","process_id":10936}
{"timestamp":"2025-08-05T08:24:26.339588","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":6156,"thread_name":"MainThread","process_id":10936}
{"timestamp":"2025-08-05T08:24:26.341585","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":6156,"thread_name":"MainThread","process_id":10936}
{"timestamp":"2025-08-05T08:24:26.343585","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":6156,"thread_name":"MainThread","process_id":10936}
{"timestamp":"2025-08-05T08:38:17.418430","level":"ERROR","logger":"src.ui.webview_app","module":"webview_app","function":"_load_app_config","line":78,"message":"加载应用配置失败: 'ConfigManager' object has no attribute 'get_config'","thread_id":19372,"thread_name":"MainThread","process_id":14208}
{"timestamp":"2025-08-05T08:38:47.057549","level":"ERROR","logger":"src.ui.webview_app","module":"webview_app","function":"_load_app_config","line":78,"message":"加载应用配置失败: 'ConfigManager' object has no attribute 'get_config'","thread_id":17148,"thread_name":"MainThread","process_id":14456}
{"timestamp":"2025-08-05T08:38:47.072074","level":"ERROR","logger":"src.ui.webview_app","module":"webview_app","function":"_load_app_config","line":78,"message":"加载应用配置失败: 'ConfigManager' object has no attribute 'get_config'","thread_id":17148,"thread_name":"MainThread","process_id":14456}
{"timestamp":"2025-08-05T08:39:58.410022","level":"ERROR","logger":"src.ui.webview_app","module":"webview_app","function":"_load_app_config","line":78,"message":"加载应用配置失败: 'ConfigManager' object has no attribute 'get_config'","thread_id":22020,"thread_name":"MainThread","process_id":1340}
{"timestamp":"2025-08-05T08:42:14.973560","level":"ERROR","logger":"src.ui.webview_app","module":"webview_app","function":"_load_app_config","line":78,"message":"加载应用配置失败: 'ConfigManager' object has no attribute 'get_config'","thread_id":5068,"thread_name":"MainThread","process_id":3752}
{"timestamp":"2025-08-05T08:42:23.610625","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":22680,"thread_name":"Thread-6 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:42:28.602739","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":23248,"thread_name":"Thread-7 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:42:33.602010","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":17116,"thread_name":"Thread-8 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:42:38.600415","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":2768,"thread_name":"Thread-9 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:42:43.602479","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":12136,"thread_name":"Thread-10 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:42:48.602140","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":22244,"thread_name":"Thread-11 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:42:50.882536","level":"ERROR","logger":"src.ui.api","module":"api","function":"get_config","line":248,"message":"获取配置失败: 'ConfigManager' object has no attribute 'get_config'","thread_id":23104,"thread_name":"Thread-12 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:42:51.803078","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.get_positions() takes 1 positional argument but 2 were given\n","thread_id":22012,"thread_name":"Thread-13 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:42:53.602451","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":7532,"thread_name":"Thread-14 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:42:54.377429","level":"ERROR","logger":"src.ui.api","module":"api","function":"get_market_data","line":357,"message":"获取市场数据失败: 'MarketDataService' object has no attribute 'get_market_data'","thread_id":12644,"thread_name":"Thread-15 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:42:54.391971","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.get_position_summary() takes 1 positional argument but 2 were given\n","thread_id":21304,"thread_name":"Thread-16 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:42:58.602489","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":18956,"thread_name":"Thread-17 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:42:59.338159","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.get_positions() takes 1 positional argument but 2 were given\n","thread_id":16240,"thread_name":"Thread-18 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:00.009959","level":"ERROR","logger":"src.ui.api","module":"api","function":"get_config","line":248,"message":"获取配置失败: 'ConfigManager' object has no attribute 'get_config'","thread_id":16404,"thread_name":"Thread-19 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:01.938046","level":"ERROR","logger":"src.ui.api","module":"api","function":"get_config","line":248,"message":"获取配置失败: 'ConfigManager' object has no attribute 'get_config'","thread_id":8908,"thread_name":"Thread-20 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:02.505351","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.get_positions() takes 1 positional argument but 2 were given\n","thread_id":13824,"thread_name":"Thread-21 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:03.600639","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":14640,"thread_name":"Thread-22 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:04.233674","level":"ERROR","logger":"src.ui.api","module":"api","function":"get_market_data","line":357,"message":"获取市场数据失败: 'MarketDataService' object has no attribute 'get_market_data'","thread_id":22620,"thread_name":"Thread-23 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:04.259924","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.get_position_summary() takes 1 positional argument but 2 were given\n","thread_id":7140,"thread_name":"Thread-24 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:08.602423","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":17376,"thread_name":"Thread-25 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:13.601897","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":16324,"thread_name":"Thread-26 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:18.600309","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":17200,"thread_name":"Thread-27 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:23.602997","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":17908,"thread_name":"Thread-28 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:27.512343","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.get_positions() takes 1 positional argument but 2 were given\n","thread_id":20936,"thread_name":"Thread-29 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:28.602673","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":10872,"thread_name":"Thread-30 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:28.801455","level":"ERROR","logger":"src.ui.api","module":"api","function":"get_config","line":248,"message":"获取配置失败: 'ConfigManager' object has no attribute 'get_config'","thread_id":3848,"thread_name":"Thread-31 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:30.154066","level":"ERROR","logger":"src.ui.api","module":"api","function":"get_config","line":248,"message":"获取配置失败: 'ConfigManager' object has no attribute 'get_config'","thread_id":5416,"thread_name":"Thread-32 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:33.312576","level":"ERROR","logger":"src.ui.api","module":"api","function":"get_market_data","line":357,"message":"获取市场数据失败: 'MarketDataService' object has no attribute 'get_market_data'","thread_id":19768,"thread_name":"Thread-33 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:33.324086","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.get_position_summary() takes 1 positional argument but 2 were given\n","thread_id":22480,"thread_name":"Thread-34 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:33.601544","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":17880,"thread_name":"Thread-35 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:33.929027","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.get_positions() takes 1 positional argument but 2 were given\n","thread_id":840,"thread_name":"Thread-36 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:35.224181","level":"ERROR","logger":"src.ui.api","module":"api","function":"get_config","line":248,"message":"获取配置失败: 'ConfigManager' object has no attribute 'get_config'","thread_id":9160,"thread_name":"Thread-37 (_call)","process_id":3752}
{"timestamp":"2025-08-05T08:43:36.929897","level":"ERROR","logger":"src.ui.webview_app","module":"webview_app","function":"_save_app_config","line":97,"message":"保存应用配置失败: 'ConfigManager' object has no attribute 'set_config'","thread_id":5068,"thread_name":"MainThread","process_id":3752}
{"timestamp":"2025-08-05T08:44:28.630172","level":"ERROR","logger":"src.ui.webview_app","module":"webview_app","function":"_load_app_config","line":78,"message":"加载应用配置失败: 'ConfigManager' object has no attribute 'get_config'","thread_id":1496,"thread_name":"MainThread","process_id":18844}
{"timestamp":"2025-08-05T08:47:18.909226","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":13328,"thread_name":"Thread-6 (_call)","process_id":6236}
{"timestamp":"2025-08-05T08:47:23.895774","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":4772,"thread_name":"Thread-7 (_call)","process_id":6236}
{"timestamp":"2025-08-05T08:47:59.231726","level":"ERROR","logger":"src.ui.webview_app","module":"webview_app","function":"_load_app_config","line":78,"message":"加载应用配置失败: dictionary update sequence element #0 has length 1; 2 is required","thread_id":15188,"thread_name":"MainThread","process_id":17688}
{"timestamp":"2025-08-05T08:48:02.269169","level":"ERROR","logger":"src.ui.webview_app","module":"webview_app","function":"_load_app_config","line":78,"message":"加载应用配置失败: dictionary update sequence element #0 has length 1; 2 is required","thread_id":5896,"thread_name":"MainThread","process_id":23384}
{"timestamp":"2025-08-05T08:48:08.284634","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":23452,"thread_name":"Thread-8 (_call)","process_id":23384}
{"timestamp":"2025-08-05T08:48:13.286256","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":17620,"thread_name":"Thread-9 (_call)","process_id":23384}
{"timestamp":"2025-08-05T08:50:30.764160","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":19152,"thread_name":"Thread-6 (_call)","process_id":16200}
{"timestamp":"2025-08-05T08:50:35.454414","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":16224,"thread_name":"Thread-7 (_call)","process_id":16200}
{"timestamp":"2025-08-05T08:50:35.464418","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":16224,"thread_name":"Thread-7 (_call)","process_id":16200}
{"timestamp":"2025-08-05T08:50:35.467412","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":16224,"thread_name":"Thread-7 (_call)","process_id":16200}
{"timestamp":"2025-08-05T08:50:35.470412","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":16224,"thread_name":"Thread-7 (_call)","process_id":16200}
{"timestamp":"2025-08-05T08:50:35.474413","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":16224,"thread_name":"Thread-7 (_call)","process_id":16200}
{"timestamp":"2025-08-05T08:50:35.480933","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":16224,"thread_name":"Thread-7 (_call)","process_id":16200}
{"timestamp":"2025-08-05T08:50:35.483937","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":16224,"thread_name":"Thread-7 (_call)","process_id":16200}
{"timestamp":"2025-08-05T08:50:35.487937","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":16224,"thread_name":"Thread-7 (_call)","process_id":16200}
{"timestamp":"2025-08-05T08:50:35.491943","level":"ERROR","logger":"src.core.scheduler.task_scheduler","module":"task_scheduler","function":"_update_next_run_time","line":634,"message":"更新下次运行时间失败: 'Job' object has no attribute 'next_run_time'","thread_id":16224,"thread_name":"Thread-7 (_call)","process_id":16200}
{"timestamp":"2025-08-05T08:50:35.757640","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":15492,"thread_name":"Thread-8 (_call)","process_id":16200}
{"timestamp":"2025-08-05T08:50:37.667438","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.stop_trading() takes 1 positional argument but 2 were given\n","thread_id":15392,"thread_name":"Thread-9 (_call)","process_id":16200}
{"timestamp":"2025-08-05T08:50:38.417985","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.get_positions() takes 1 positional argument but 2 were given\n","thread_id":22684,"thread_name":"Thread-10 (_call)","process_id":16200}
{"timestamp":"2025-08-05T08:50:39.106400","level":"ERROR","logger":"src.ui.api","module":"api","function":"get_config","line":248,"message":"获取配置失败: 'ConfigManager' object has no attribute 'get_config'","thread_id":6552,"thread_name":"Thread-11 (_call)","process_id":16200}
{"timestamp":"2025-08-05T08:50:40.562054","level":"ERROR","logger":"src.ui.api","module":"api","function":"get_config","line":248,"message":"获取配置失败: 'ConfigManager' object has no attribute 'get_config'","thread_id":7112,"thread_name":"Thread-12 (_call)","process_id":16200}
{"timestamp":"2025-08-05T08:50:40.759037","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":14156,"thread_name":"Thread-13 (_call)","process_id":16200}
{"timestamp":"2025-08-05T08:50:43.930396","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.set_config() missing 1 required positional argument: 'value'\n","thread_id":14836,"thread_name":"Thread-14 (_call)","process_id":16200}
{"timestamp":"2025-08-05T08:52:37.940102","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":13100,"thread_name":"Thread-6 (_call)","process_id":22468}
{"timestamp":"2025-08-05T08:52:42.935323","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":12004,"thread_name":"Thread-7 (_call)","process_id":22468}
{"timestamp":"2025-08-05T08:52:47.933690","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":22956,"thread_name":"Thread-8 (_call)","process_id":22468}
{"timestamp":"2025-08-05T08:52:52.938416","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":13964,"thread_name":"Thread-9 (_call)","process_id":22468}
{"timestamp":"2025-08-05T08:52:57.935399","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":22752,"thread_name":"Thread-10 (_call)","process_id":22468}
{"timestamp":"2025-08-05T08:53:02.933181","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":3852,"thread_name":"Thread-11 (_call)","process_id":22468}
{"timestamp":"2025-08-05T08:53:07.932302","level":"ERROR","logger":"pywebview","module":"util","function":"_call","line":245,"message":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\webview\\util.py\", line 241, in _call\n    result = func(*func_params)\n             ^^^^^^^^^^^^^^^^^^\nTypeError: WebviewAPI.ping() takes 1 positional argument but 2 were given\n","thread_id":2768,"thread_name":"Thread-12 (_call)","process_id":22468}
