/* 全局样式重置和基础设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    background-color: #f0f2f5;
    color: #000000d9;
    line-height: 1.5715;
    font-size: 14px;
    font-variant: tabular-nums;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
}

#app {
    height: 100vh;
    overflow: hidden;
}

/* 主布局样式 */
.main-layout {
    height: 100vh;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
    padding: 0;
    height: 64px;
    line-height: 64px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    height: 100%;
}

.brand {
    display: flex;
    align-items: center;
    gap: 12px;
}

.brand-text h1 {
    color: white;
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    line-height: 1;
}

.brand-text span {
    color: rgba(255, 255, 255, 0.85);
    font-size: 12px;
    line-height: 1;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.env-switch {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 4px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-btn {
    color: white !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
}

.status-btn:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
}

/* 侧边栏样式 */
.sider {
    background: #fff;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

.sider-content {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.sider-trigger {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
}

/* 内容区域样式 */
.content {
    background: #f0f2f5;
    padding: 24px;
    overflow-y: auto;
}

.content-wrapper {
    max-width: 1400px;
    margin: 0 auto;
}

/* 页面头部样式 */
.page-header {
    background: white;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.page-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.page-title h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: #262626;
}

.page-title p {
    margin: 0;
    color: #8c8c8c;
    font-size: 14px;
}

/* 指标卡片样式 */
.metrics-section {
    margin-bottom: 24px;
}

.metric-card {
    height: 100%;
    transition: all 0.3s ease;
}

.metric-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.metric-change {
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.change-label {
    color: #8c8c8c;
    font-size: 12px;
}

.metric-extra {
    margin-top: 8px;
}

/* AI卡片特殊样式 */
.ai-card .ai-status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.ai-card .ai-status-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.ai-metrics {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.ai-metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-metric-item .label {
    color: #8c8c8c;
    font-size: 12px;
}

.ai-metric-item .value {
    font-size: 12px;
    color: #262626;
}

/* 数据面板样式 */
.data-section {
    margin-top: 24px;
}

.data-panel {
    height: 100%;
}

.market-data-content {
    padding-top: 16px;
}

.market-details {
    margin-top: 16px;
}

.position-summary {
    padding-top: 16px;
}

.position-overview {
    margin-bottom: 16px;
}

.position-list {
    max-height: 300px;
    overflow-y: auto;
}

.position-pnl {
    text-align: right;
}

/* 交易控制页面样式 */
.control-card, .params-card {
    height: 100%;
}

.trading-controls {
    padding: 8px 0;
}

.control-section {
    margin-bottom: 24px;
}

.control-section:last-child {
    margin-bottom: 0;
}

.control-section h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #262626;
}

.control-buttons {
    margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        padding: 0 16px;
    }

    .brand-text h1 {
        font-size: 18px;
    }

    .brand-text span {
        display: none;
    }

    .header-actions {
        gap: 8px;
    }

    .content {
        padding: 16px;
    }

    .page-header {
        padding: 16px;
    }

    .page-header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
}

@media (max-width: 576px) {
    .metrics-section .ant-col {
        margin-bottom: 16px;
    }

    .data-section .ant-col {
        margin-bottom: 16px;
    }
}

/* 自定义Ant Design组件样式 */
.ant-layout-header {
    padding: 0 !important;
}

.ant-menu-inline {
    border-right: none;
}

.ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.ant-card-head {
    border-bottom: 1px solid #f0f0f0;
}

.ant-statistic-title {
    color: #8c8c8c;
    font-size: 14px;
    margin-bottom: 4px;
}

.ant-statistic-content {
    color: #262626;
    font-size: 24px;
    font-weight: 600;
}

.ant-progress-line {
    margin: 8px 0 4px 0;
}

.ant-descriptions-item-label {
    color: #8c8c8c;
    font-weight: 400;
}

.ant-descriptions-item-content {
    color: #262626;
}

/* 页面内容样式 */
.page-content {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 加载状态样式 */
.ant-spin-container {
    transition: opacity 0.3s;
}

/* 状态指示器样式 */
.ant-badge-status-dot {
    width: 8px;
    height: 8px;
}

.ant-badge-status-success {
    background-color: #52c41a;
}

.ant-badge-status-processing {
    background-color: #1890ff;
}

.ant-badge-status-error {
    background-color: #ff4d4f;
}

.ant-badge-status-warning {
    background-color: #faad14;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    height: 100%;
    max-width: 1400px;
    margin: 0 auto;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 15px;
}

.brand-icon {
    width: 45px;
    height: 45px;
    background: rgba(255,255,255,0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5em;
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-name {
    font-size: 1.4em;
    font-weight: 700;
    line-height: 1;
}

.brand-subtitle {
    font-size: 0.8em;
    opacity: 0.8;
    font-weight: 400;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* 环境切换开关 */
.env-switch {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255,255,255,0.1);
    padding: 8px 15px;
    border-radius: 25px;
    border: 1px solid rgba(255,255,255,0.2);
}

.env-label {
    font-size: 0.85em;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.env-label.active {
    opacity: 1;
    font-weight: 600;
}

.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255,255,255,0.3);
    transition: .3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

input:checked + .slider {
    background-color: #4CAF50;
}

input:checked + .slider:before {
    transform: translateX(20px);
}

/* 连接状态指示器 */
.connection-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    background: rgba(255,255,255,0.1);
    border-radius: 20px;
    font-size: 0.9em;
    border: 1px solid rgba(255,255,255,0.2);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #f44336;
    animation: pulse 2s infinite;
}

.connection-indicator.connected .status-dot {
    background: #4CAF50;
}

.connection-indicator.connecting .status-dot {
    background: #ff9800;
}

@keyframes pulse {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
    100% { opacity: 1; transform: scale(1); }
}

/* 主容器 */
.main-container {
    display: flex;
    margin-top: 60px;
    min-height: calc(100vh - 60px);
}

/* 侧边栏 */
.sidebar {
    width: 250px;
    background: white;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    position: fixed;
    left: 0;
    top: 60px;
    bottom: 0;
    overflow-y: auto;
}

.sidebar-menu {
    padding: 20px 0;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.menu-item:hover {
    background-color: #f8f9fa;
    border-left-color: #667eea;
}

.menu-item.active {
    background-color: #e3f2fd;
    border-left-color: #667eea;
    color: #667eea;
}

.menu-item i {
    margin-right: 12px;
    width: 20px;
    text-align: center;
}

/* 内容区域 */
.content {
    flex: 1;
    margin-left: 250px;
    padding: 30px;
    background-color: #f5f7fa;
}

.page-header {
    margin-bottom: 30px;
}

.page-header h1 {
    font-size: 2.2em;
    color: #333;
    margin-bottom: 5px;
}

.page-header p {
    color: #666;
    font-size: 1.1em;
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 状态卡片 */
.status-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.status-card {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    transition: transform 0.3s ease;
}

.status-card:hover {
    transform: translateY(-2px);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
}

.card-icon i {
    color: white;
    font-size: 1.5em;
}

.card-content h3 {
    font-size: 1.1em;
    color: #666;
    margin-bottom: 5px;
}

.card-content p {
    font-size: 1.3em;
    font-weight: bold;
    color: #333;
}

/* 数据区域 */
.data-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.data-card {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.data-card h3 {
    font-size: 1.3em;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
}

/* 控制面板 */
.control-panel {
    background: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.control-section {
    margin-bottom: 30px;
}

.control-section h3 {
    font-size: 1.3em;
    color: #333;
    margin-bottom: 15px;
}

.control-buttons {
    display: flex;
    gap: 15px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1em;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn i {
    margin-right: 8px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.btn-success {
    background: #4CAF50;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #45a049;
    transform: translateY(-1px);
}

.btn-danger {
    background: #f44336;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #da190b;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #5a6268;
    transform: translateY(-1px);
}

/* 交易对选择 */
.trading-pairs {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.pair-item {
    display: flex;
    align-items: center;
}

.pair-item input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
}

.pair-item label {
    font-weight: 500;
    cursor: pointer;
}

/* 交易状态 */
.trading-status {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.trading-status h3 {
    font-size: 1.3em;
    color: #333;
    margin-bottom: 15px;
}

/* 持仓表格 */
.positions-table {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
}

tr:hover {
    background-color: #f8f9fa;
}

.no-data {
    text-align: center;
    color: #666;
    font-style: italic;
}

/* 配置表单 */
.config-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.config-section {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.config-section h3 {
    font-size: 1.3em;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
}

.config-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 500;
    margin-bottom: 5px;
    color: #333;
}

.form-group input {
    padding: 10px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 1em;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

.config-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* 日志区域 */
.logs-section {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.logs-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.logs-controls select {
    padding: 8px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 1em;
}

.logs-container {
    height: 400px;
    overflow-y: auto;
    background: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
}

.logs-content {
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .content {
        margin-left: 0;
        padding: 20px;
    }
    
    .status-cards {
        grid-template-columns: 1fr;
    }
    
    .data-section {
        grid-template-columns: 1fr;
    }
    
    .config-sections {
        grid-template-columns: 1fr;
    }
    
    .control-buttons {
        flex-direction: column;
    }
    
    .trading-pairs {
        flex-direction: column;
        gap: 10px;
    }
}

/* 加载动画 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 50px;
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.9em;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* 成功/错误消息 */
.message {
    padding: 15px;
    border-radius: 6px;
    margin: 15px 0;
    font-weight: 500;
}

.message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.message.warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* 市场数据和仓位汇总样式 */
.market-item, .summary-item, .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.market-item:last-child, .summary-item:last-child, .status-item:last-child {
    border-bottom: none;
}

.market-item .label, .summary-item .label, .status-item .label {
    font-weight: 500;
    color: #666;
}

.market-item .value, .summary-item .value, .status-item .value {
    font-weight: 600;
    color: #333;
}

.value.positive {
    color: #4CAF50;
}

.value.negative {
    color: #f44336;
}

.value.success {
    color: #4CAF50;
}

.value.error {
    color: #f44336;
}

/* 小按钮样式 */
.btn-sm {
    padding: 6px 12px;
    font-size: 0.875em;
}

/* 日志样式 */
.log-entry {
    display: flex;
    align-items: center;
    padding: 5px 0;
    font-size: 0.9em;
    border-bottom: 1px solid #eee;
}

.log-entry .timestamp {
    color: #666;
    margin-right: 10px;
    min-width: 150px;
}

.log-entry .level {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
    font-weight: bold;
    margin-right: 10px;
    min-width: 50px;
    text-align: center;
}

.log-entry .level.info {
    background-color: #e3f2fd;
    color: #1976d2;
}

.log-entry .level.warning {
    background-color: #fff3e0;
    color: #f57c00;
}

.log-entry .level.error {
    background-color: #ffebee;
    color: #d32f2f;
}

.log-entry .message {
    flex: 1;
    color: #333;
}

/* 资产卡片样式 */
.asset-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.asset-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 25px;
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.asset-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.asset-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.asset-card:nth-child(2) {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
}

.asset-card:nth-child(2):hover {
    box-shadow: 0 12px 35px rgba(240, 147, 251, 0.4);
}

.asset-card:nth-child(3) {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
}

.asset-card:nth-child(3):hover {
    box-shadow: 0 12px 35px rgba(79, 172, 254, 0.4);
}

.asset-card .asset-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.asset-card .asset-icon i {
    font-size: 24px;
    color: white;
}

.asset-card .asset-content h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 500;
    opacity: 0.9;
}

.asset-card .asset-value {
    font-size: 28px;
    font-weight: 700;
    margin: 0;
    line-height: 1.2;
}

.asset-card .asset-currency {
    font-size: 14px;
    opacity: 0.8;
    font-weight: 500;
}

/* 新仪表盘样式 */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 2px solid #f0f0f0;
}

.header-left h1 {
    margin: 0;
    font-size: 28px;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-left h1 i {
    color: #3498db;
}

.header-left .subtitle {
    margin: 5px 0 0 0;
    color: #7f8c8d;
    font-size: 14px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.last-update {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #7f8c8d;
    font-size: 14px;
}

.last-update i {
    animation: spin 2s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #d4edda;
    color: #155724;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.connection-status.disconnected {
    background: #f8d7da;
    color: #721c24;
}

.connection-status i {
    font-size: 8px;
}

/* 主要指标卡片 */
.main-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-bottom: 30px;
}

.metric-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px 15px;
    border-bottom: 1px solid #f8f9fa;
}

.card-header h3 {
    margin: 0;
    font-size: 18px;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header h3 i {
    color: #3498db;
}

.card-actions {
    display: flex;
    gap: 8px;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border: none;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    background: #e9ecef;
    transform: scale(1.05);
}

.card-body {
    padding: 20px 25px 25px;
}

/* 资产网格 */
.asset-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.asset-item {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.asset-item.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    grid-column: 1 / -1;
}

.asset-item:hover {
    transform: translateY(-2px);
}

.asset-label {
    font-size: 14px;
    color: #7f8c8d;
    margin-bottom: 8px;
    font-weight: 500;
}

.asset-item.primary .asset-label {
    color: rgba(255, 255, 255, 0.9);
}

.asset-value {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 4px;
}

.asset-item.primary .asset-value {
    color: white;
    font-size: 32px;
}

.asset-unit {
    font-size: 12px;
    color: #95a5a6;
    font-weight: 500;
}

.asset-item.primary .asset-unit {
    color: rgba(255, 255, 255, 0.8);
}

/* 交易状态 */
.trading-status .card-header {
    position: relative;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #e74c3c;
    animation: pulse 2s infinite;
}

.status-indicator.active .status-dot {
    background: #27ae60;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.status-item:hover {
    background: #e9ecef;
    transform: translateX(2px);
}

.status-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.status-icon.system { background: linear-gradient(135deg, #667eea, #764ba2); }
.status-icon.scheduler { background: linear-gradient(135deg, #f093fb, #f5576c); }
.status-icon.positions { background: linear-gradient(135deg, #4facfe, #00f2fe); }
.status-icon.ai { background: linear-gradient(135deg, #43e97b, #38f9d7); }

.status-info {
    flex: 1;
}

.status-label {
    font-size: 12px;
    color: #7f8c8d;
    margin-bottom: 4px;
}

.status-value {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .header-right {
        width: 100%;
        justify-content: space-between;
    }

    .main-metrics {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .asset-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .asset-item.primary {
        grid-column: 1;
    }

    .status-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
}

/* 数据面板 */
.data-panels {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 25px;
    margin-bottom: 30px;
}

.data-panel {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
}

.data-panel:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px 15px;
    border-bottom: 1px solid #f8f9fa;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.panel-header h3 {
    margin: 0;
    font-size: 16px;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
}

.panel-header h3 i {
    color: #3498db;
}

.panel-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.symbol-selector, .period-selector {
    padding: 6px 12px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: white;
    font-size: 12px;
    color: #495057;
    cursor: pointer;
}

.btn-small {
    padding: 6px 12px;
    border: none;
    background: #3498db;
    color: white;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-small:hover {
    background: #2980b9;
    transform: scale(1.05);
}

.panel-body {
    padding: 20px 25px 25px;
}

/* 市场数据网格 */
.market-data-grid {
    display: grid;
    gap: 15px;
}

.market-loading, .position-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 40px 20px;
    color: #7f8c8d;
    font-size: 14px;
}

.market-loading i, .position-loading i {
    color: #3498db;
}

/* 持仓汇总网格 */
.position-summary-grid {
    display: grid;
    gap: 12px;
}

/* 性能统计网格 */
.performance-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.perf-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.perf-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.perf-label {
    font-size: 12px;
    color: #7f8c8d;
    margin-bottom: 8px;
    font-weight: 500;
}

.perf-value {
    font-size: 20px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 4px;
}

.perf-value.positive {
    color: #27ae60;
}

.perf-value.negative {
    color: #e74c3c;
}

.perf-unit {
    font-size: 11px;
    color: #95a5a6;
    font-weight: 500;
}

/* 数据面板响应式 */
@media (max-width: 1200px) {
    .data-panels {
        grid-template-columns: 1fr 1fr;
    }

    .performance-panel {
        grid-column: 1 / -1;
    }
}

@media (max-width: 768px) {
    .data-panels {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .performance-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .panel-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .panel-controls {
        width: 100%;
        justify-content: flex-end;
    }
}

/* 市场数据样式 */
.market-item.primary {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    margin-bottom: 15px;
}

.market-label {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 8px;
}

.market-price {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
}

.market-change {
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.market-stats {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 10px;
}

.stat-item {
    text-align: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-label {
    font-size: 11px;
    color: #7f8c8d;
    margin-bottom: 4px;
}

.stat-value {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
}

/* 持仓汇总样式 */
.position-overview {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.position-stat {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.position-stat .stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.position-stat .stat-icon.pnl {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.position-stat .stat-info {
    flex: 1;
}

.position-stat .stat-value {
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 2px;
}

.position-stat .stat-label {
    font-size: 12px;
    color: #7f8c8d;
}

.position-details {
    display: grid;
    gap: 8px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-size: 13px;
    color: #7f8c8d;
}

.detail-value {
    font-size: 13px;
    font-weight: 600;
    color: #2c3e50;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .market-stats {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .position-overview {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .market-price {
        font-size: 24px;
    }
}
